-- إصلاح مشكلة exports المفقودة
-- Fix for missing exports

if not IsDuplicityVersion() then
    -- Client-side exports fix
    
    -- دالة آمنة لحذف المركبات
    local function safeDeleteVehicle(vehicle)
        local success = false
        
        -- محاولة استخدام AdvancedParking أولاً
        success = pcall(function()
            exports.AdvancedParking:DeleteVehicle(vehicle, false)
        end)
        
        if not success then
            -- محاولة استخدام AdvancedParking بطريقة أخرى
            success = pcall(function()
                exports.AdvancedParking.DeleteVehicle(vehicle, false)
            end)
        end
        
        if not success then
            -- استخدام حذف عادي
            if DoesEntityExist(vehicle) then
                DeleteEntity(vehicle)
                print('^3[EXPORTS FIX] ^7Used standard delete for vehicle')
            end
        else
            print('^2[EXPORTS FIX] ^7Used AdvancedParking for vehicle deletion')
        end
    end
    
    -- دالة آمنة للحصول على المركبات المرفقة
    local function safeGetAttachedVehicles(vehicle)
        local attachedVehicles = {}
        
        local success = pcall(function()
            attachedVehicles = exports.ab_Logs:GetAttachedVehicles(vehicle) or {}
        end)
        
        if not success then
            success = pcall(function()
                attachedVehicles = exports.ab_Logs.GetAttachedVehicles(vehicle) or {}
            end)
        end
        
        if not success then
            print('^3[EXPORTS FIX] ^7ab_Logs not available, returning empty array')
            return {}
        end
        
        print('^2[EXPORTS FIX] ^7Found ' .. #attachedVehicles .. ' attached vehicles')
        return attachedVehicles
    end
    
    -- تصدير الدوال الآمنة
    exports('safeDeleteVehicle', safeDeleteVehicle)
    exports('safeGetAttachedVehicles', safeGetAttachedVehicles)
    
    -- أمر لاختبار حذف المركبة
    RegisterCommand('test_delete_vehicle', function()
        local playerPed = PlayerPedId()
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        
        if vehicle == 0 then
            print('^1[EXPORTS FIX] ^7You must be in a vehicle!')
            return
        end
        
        print('^3[EXPORTS FIX] ^7Testing vehicle deletion...')
        
        -- اختبار الحصول على المركبات المرفقة
        local attachedVehicles = safeGetAttachedVehicles(vehicle)
        print('^3[EXPORTS FIX] ^7Attached vehicles: ' .. #attachedVehicles)
        
        -- اختبار حذف المركبة
        safeDeleteVehicle(vehicle)
        
        print('^2[EXPORTS FIX] ^7Vehicle deletion test completed')
    end, false)
    
    -- أمر لاختبار جميع exports
    RegisterCommand('test_all_exports', function()
        print('^3[EXPORTS FIX] ^7Testing all exports...')
        
        -- اختبار AdvancedParking
        local advancedParkingSuccess = pcall(function()
            -- مجرد اختبار وجود export
            local test = exports.AdvancedParking
            return test ~= nil
        end)
        print('^' .. (advancedParkingSuccess and '2' or '1') .. '[EXPORTS FIX] ^7AdvancedParking: ' .. (advancedParkingSuccess and 'OK' or 'MISSING'))
        
        -- اختبار ab_Logs
        local abLogsSuccess = pcall(function()
            local test = exports.ab_Logs
            return test ~= nil
        end)
        print('^' .. (abLogsSuccess and '2' or '1') .. '[EXPORTS FIX] ^7ab_Logs: ' .. (abLogsSuccess and 'OK' or 'MISSING'))
        
        -- اختبار OscarCounty_Notifications
        local notificationsSuccess = pcall(function()
            exports.OscarCounty_Notifications:showAttention('info', 'Test notification')
        end)
        print('^' .. (notificationsSuccess and '2' or '1') .. '[EXPORTS FIX] ^7Notifications: ' .. (notificationsSuccess and 'OK' or 'MISSING'))
        
        -- اختبار OscarCounty_Web
        local webSuccess = pcall(function()
            local test = exports.OscarCounty_Web
            return test ~= nil
        end)
        print('^' .. (webSuccess and '2' or '1') .. '[EXPORTS FIX] ^7Web: ' .. (webSuccess and 'OK' or 'MISSING'))
        
        print('^2[EXPORTS FIX] ^7All exports tested')
    end, false)
    
    print('^2[EXPORTS FIX] ^7Exports fix loaded')
    print('^3[EXPORTS FIX] ^7Use: /test_delete_vehicle or /test_all_exports')
end
