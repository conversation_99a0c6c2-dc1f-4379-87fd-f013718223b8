-- ``````````` ## Development By el8rbawY ## ```````````
-- <PERSON><PERSON>t for OscarCounty Garage System
-- This script creates/modifies the necessary database tables

-- تحديث جدول owned_vehicles لإضافة الأعمدة المطلوبة إذا لم تكن موجودة
ALTER TABLE `owned_vehicles` 
ADD COLUMN IF NOT EXISTS `impounded` tinyint(1) NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS `impound_reason` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `impound_date` timestamp NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `garage_type` varchar(50) DEFAULT 'car',
ADD COLUMN IF NOT EXISTS `last_position` longtext DEFAULT NULL;

-- إنشاء جدول لحفظ إعدادات الكراجات (اختياري)
CREATE TABLE IF NOT EXISTS `garage_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `garage_name` varchar(100) NOT NULL,
  `garage_type` varchar(50) NOT NULL DEFAULT 'car',
  `job_required` varchar(50) DEFAULT 'civ',
  `coords_x` float NOT NULL,
  `coords_y` float NOT NULL,
  `coords_z` float NOT NULL,
  `spawn_x` float NOT NULL,
  `spawn_y` float NOT NULL,
  `spawn_z` float NOT NULL,
  `spawn_heading` float NOT NULL DEFAULT 0,
  `blip_id` int(11) DEFAULT 357,
  `blip_color` int(11) DEFAULT 60,
  `enabled` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `garage_name` (`garage_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول لحفظ سجل استخدام الكراجات (اختياري)
CREATE TABLE IF NOT EXISTS `garage_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(60) NOT NULL,
  `action` varchar(50) NOT NULL,
  `vehicle_plate` varchar(20) NOT NULL,
  `garage_name` varchar(100) DEFAULT NULL,
  `garage_type` varchar(50) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `details` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `identifier` (`identifier`),
  KEY `vehicle_plate` (`vehicle_plate`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول لكراجات المنازل الخاصة (اختياري)
CREATE TABLE IF NOT EXISTS `house_garages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `house_id` varchar(100) NOT NULL,
  `owner` varchar(60) NOT NULL,
  `garage_type` varchar(50) NOT NULL DEFAULT 'car',
  `coords_x` float NOT NULL,
  `coords_y` float NOT NULL,
  `coords_z` float NOT NULL,
  `spawn_x` float NOT NULL,
  `spawn_y` float NOT NULL,
  `spawn_z` float NOT NULL,
  `spawn_heading` float NOT NULL DEFAULT 0,
  `max_vehicles` int(11) NOT NULL DEFAULT 2,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `house_garage` (`house_id`, `garage_type`),
  KEY `owner` (`owner`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بعض البيانات التجريبية للكراجات (اختياري)
INSERT IGNORE INTO `garage_settings` (`garage_name`, `garage_type`, `job_required`, `coords_x`, `coords_y`, `coords_z`, `spawn_x`, `spawn_y`, `spawn_z`, `spawn_heading`, `blip_id`, `blip_color`) VALUES
('Legion Square Garage', 'car', 'civ', 823.343, -1374.2875, 25.8374, 823.307, -1371.8209, 26.1374, 359.6377, 357, 60),
('Sandy Shores Garage', 'car', 'civ', 1722.7451, 3713.6497, 33.9095, 1722.7451, 3713.6497, 34.2095, 21.1356, 357, 60),
('Paleto Bay Garage', 'car', 'civ', 69.1234, 6587.7031, 30.5557, 70.1484, 6586.603, 31.2557, 225.2695, 357, 60),
('LSPD Garage', 'car', 'police', 569.8546, 4.9426, 70.1131, 566.4333, 6.1947, 70.6131, 68.2411, 357, 29),
('EMS Garage', 'car', 'ambulance', -291.6007, -637.7404, 32.1741, -291.7202, -631.6907, 32.7741, 0.3833, 357, 1),
('Mechanic Garage', 'car', 'mechanic', -365.8638, -109.1873, 38.2969, -368.3556, -117.1661, 38.6965, 160.2025, 357, 5),
('Truck Garage', 'truck', 'civ', 924.0906, -1563.9518, 30.4255, 909.8285, -1564.1172, 30.7801, 89.3061, 477, 60),
('Boat Garage', 'boat', 'civ', -794.9193, -1510.9023, 1.5955, -797.3044, -1502.9792, 1.1755, 109.0463, 427, 0),
('Helicopter Garage', 'helicopter', 'civ', -1016.864, -3506.2617, 14.1434, -1016.864, -3506.2617, 14.1434, 62.998, 43, 0),
('Aircraft Garage', 'aircraft', 'civ', -1072.0404, -3499.1912, 14.1434, -1072.0404, -3499.1912, 14.1434, 333.2326, 307, 0),
('Impound Lot', 'car', 'impounded', 409.6076, -1623.163, 29.0064, 395.9602, -1644.7235, 29.2919, 317.6961, 526, 47);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS `idx_owned_vehicles_impounded` ON `owned_vehicles` (`impounded`);
CREATE INDEX IF NOT EXISTS `idx_owned_vehicles_garage_type` ON `owned_vehicles` (`garage_type`);
CREATE INDEX IF NOT EXISTS `idx_owned_vehicles_owner_stored` ON `owned_vehicles` (`owner`, `stored`);

-- تحديث المركبات الموجودة لتعيين garage_type الافتراضي
UPDATE `owned_vehicles` SET `garage_type` = 'car' WHERE `garage_type` IS NULL OR `garage_type` = '';

-- إضافة تعليق على الجداول
ALTER TABLE `owned_vehicles` COMMENT = 'Updated for OscarCounty Garage System';
ALTER TABLE `garage_settings` COMMENT = 'Garage configuration settings';
ALTER TABLE `garage_logs` COMMENT = 'Garage usage logs';
ALTER TABLE `house_garages` COMMENT = 'Private house garages';
