-- إصلاح خاص لـ oxmysql
-- oxmysql specific fix

if IsDuplicityVersion() then
    -- Server-side oxmysql fix
    
    local MySQL = nil
    local isReady = false
    
    -- تحميل oxmysql
    Citizen.CreateThread(function()
        Citizen.Wait(500)
        
        if GetResourceState('oxmysql') == 'started' then
            local success, result = pcall(function()
                return exports.oxmysql
            end)
            
            if success and result then
                MySQL = result
                isReady = true
                print('^2[OXMYSQL FIX] ^7oxmysql loaded and ready')
                
                -- اختبار سريع
                MySQL.execute('SELECT 1 as test', {}, function(result)
                    if result then
                        print('^2[OXMYSQL FIX] ^7✓ Connection test successful')
                    else
                        print('^1[OXMYSQL FIX] ^7✗ Connection test failed')
                    end
                end)
            else
                print('^1[OXMYSQL FIX] ^7Failed to load oxmysql')
            end
        else
            print('^3[OXMYSQL FIX] ^7oxmysql not started, state: ' .. GetResourceState('oxmysql'))
        end
    end)
    
    -- دوال مساعدة لـ oxmysql
    local function oxExecute(query, params, callback)
        if not isReady or not MySQL then
            print('^1[OXMYSQL FIX] ^7oxmysql not ready')
            if callback then callback(0) end
            return
        end
        
        MySQL.execute(query, params, callback or function() end)
    end
    
    local function oxFetchAll(query, params, callback)
        if not isReady or not MySQL then
            print('^1[OXMYSQL FIX] ^7oxmysql not ready')
            if callback then callback({}) end
            return
        end
        
        MySQL.execute(query, params, callback or function() end)
    end
    
    -- تصدير دوال oxmysql
    exports('oxExecute', oxExecute)
    exports('oxFetchAll', oxFetchAll)
    exports('isOxReady', function() return isReady end)
    exports('getOxMySQL', function() return MySQL end)
    
    -- أمر للاختبار
    RegisterCommand('test_oxmysql', function(source)
        if source == 0 then
            print('^3[OXMYSQL FIX] ^7Testing oxmysql...')
            print('^3[OXMYSQL FIX] ^7Ready: ' .. tostring(isReady))
            print('^3[OXMYSQL FIX] ^7State: ' .. GetResourceState('oxmysql'))
            
            if isReady then
                oxFetchAll('SELECT 1 as test', {}, function(result)
                    if result and result[1] then
                        print('^2[OXMYSQL FIX] ^7✓ Test query successful')
                    else
                        print('^1[OXMYSQL FIX] ^7✗ Test query failed')
                    end
                end)
            end
        end
    end, true)
    
    -- إنشاء جدول owned_vehicles إذا لم يكن موجود
    Citizen.CreateThread(function()
        Citizen.Wait(3000) -- انتظار أطول لـ oxmysql
        
        if isReady then
            print('^3[OXMYSQL FIX] ^7Creating owned_vehicles table if not exists...')
            
            local createQuery = [[
                CREATE TABLE IF NOT EXISTS `owned_vehicles` (
                    `owner` varchar(60) NOT NULL,
                    `plate` varchar(12) NOT NULL,
                    `vehicle` longtext,
                    `stored` tinyint(1) NOT NULL DEFAULT 1,
                    `impounded` tinyint(1) NOT NULL DEFAULT 0,
                    `garage_type` varchar(50) DEFAULT 'car',
                    `job` varchar(50) DEFAULT 'civ',
                    PRIMARY KEY (`plate`),
                    KEY `owner` (`owner`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ]]
            
            oxExecute(createQuery, {}, function(result)
                print('^2[OXMYSQL FIX] ^7Table creation completed')
            end)
            
            -- إضافة أعمدة إذا لم تكن موجودة
            Citizen.Wait(1000)
            oxExecute('ALTER TABLE owned_vehicles ADD COLUMN IF NOT EXISTS `impounded` tinyint(1) NOT NULL DEFAULT 0', {}, function() end)
            oxExecute('ALTER TABLE owned_vehicles ADD COLUMN IF NOT EXISTS `garage_type` varchar(50) DEFAULT "car"', {}, function() end)
            oxExecute('ALTER TABLE owned_vehicles ADD COLUMN IF NOT EXISTS `job` varchar(50) DEFAULT "civ"', {}, function() end)
        end
    end)
    
    print('^2[OXMYSQL FIX] ^7oxmysql fix loaded')
end
