-- ملف اختبار سريع للتأكد من عمل السكربت
-- يمكن تشغيله من F8 في اللعبة

-- اختبار الأحداث الأساسية
RegisterCommand('test_garage', function()
    print('^2[GARAGE TEST] ^7Starting garage system test...')
    
    -- اختبار 1: التحقق من تحميل ESX
    local ESX = exports.es_extended.getSharedObject()
    if ESX then
        print('^2[TEST 1] ^7ESX loaded successfully ✓')
    else
        print('^1[TEST 1] ^7ESX not found ✗')
        return
    end
    
    -- اختبار 2: التحقق من بيانات اللاعب
    if ESX.PlayerData and ESX.PlayerData.identifier then
        print('^2[TEST 2] ^7Player data loaded ✓')
        print('^3[INFO] ^7Player ID: ' .. ESX.PlayerData.identifier)
        print('^3[INFO] ^7Player Job: ' .. (ESX.PlayerData.job.name or 'unknown'))
    else
        print('^1[TEST 2] ^7Player data not loaded ✗')
    end
    
    -- اختبار 3: التحقق من الكراجات
    if client and client.js then
        print('^2[TEST 3] ^7Client script loaded ✓')
    else
        print('^3[TEST 3] ^7Client script status unknown')
    end
    
    -- اختبار 4: إرسال حدث اختبار للسيرفر
    TriggerServerEvent('HyperScript_Garage:handleGeneral-server', 'test', {})
    print('^2[TEST 4] ^7Test event sent to server ✓')
    
    print('^2[GARAGE TEST] ^7Test completed!')
end, false)

-- اختبار الكراجات القريبة
RegisterCommand('test_nearby_garages', function()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    print('^2[GARAGE TEST] ^7Checking nearby garages...')
    print('^3[INFO] ^7Player position: ' .. playerCoords.x .. ', ' .. playerCoords.y .. ', ' .. playerCoords.z)
    
    -- قائمة الكراجات للاختبار (من client.js)
    local testGarages = {
        { name = 'Legion Square', x = 823.343, y = -1374.2875, z = 25.8374 },
        { name = 'Sandy Shores', x = 1722.7451, y = 3713.6497, z = 33.9095 },
        { name = 'Paleto Bay', x = 69.1234, y = 6587.7031, z = 30.5557 }
    }
    
    local nearbyCount = 0
    for _, garage in pairs(testGarages) do
        local distance = GetDistanceBetweenCoords(
            playerCoords.x, playerCoords.y, playerCoords.z,
            garage.x, garage.y, garage.z, true
        )
        
        if distance < 100 then
            nearbyCount = nearbyCount + 1
            print('^2[NEARBY] ^7' .. garage.name .. ' - Distance: ' .. math.floor(distance) .. 'm')
        end
    end
    
    if nearbyCount > 0 then
        print('^2[RESULT] ^7Found ' .. nearbyCount .. ' nearby garages ✓')
    else
        print('^3[RESULT] ^7No garages nearby (try teleporting to a garage location)')
    end
end, false)

-- اختبار قاعدة البيانات (للسيرفر)
RegisterCommand('test_database', function()
    print('^2[DATABASE TEST] ^7Testing database connection...')
    TriggerServerEvent('garage:test:database')
end, false)

-- اختبار الواجهة
RegisterCommand('test_ui', function()
    print('^2[UI TEST] ^7Testing user interface...')
    
    -- محاولة فتح الواجهة
    SendNUIMessage({
        type = 'test',
        data = {
            message = 'Test message from F8 command',
            timestamp = GetGameTimer()
        }
    })
    
    print('^2[UI TEST] ^7Test message sent to UI ✓')
end, false)

-- اختبار الإعدادات
RegisterCommand('test_config', function()
    print('^2[CONFIG TEST] ^7Testing configuration...')
    
    -- التحقق من وجود الإعدادات
    if GarageConfig then
        print('^2[CONFIG] ^7Configuration loaded ✓')
        print('^3[INFO] ^7Impound price: $' .. (GarageConfig.General.impoundedPrice or 'unknown'))
        print('^3[INFO] ^7Max vehicles: ' .. (GarageConfig.General.maxVehiclesPerPlayer or 'unknown'))
        print('^3[INFO] ^7Blips enabled: ' .. tostring(GarageConfig.General.enableBlips))
    else
        print('^1[CONFIG] ^7Configuration not loaded ✗')
    end
end, false)

-- معلومات المساعدة
RegisterCommand('garage_help', function()
    print('^2[GARAGE HELP] ^7Available test commands:')
    print('^3/test_garage ^7- Run basic system test')
    print('^3/test_nearby_garages ^7- Check for nearby garages')
    print('^3/test_database ^7- Test database connection')
    print('^3/test_ui ^7- Test user interface')
    print('^3/test_config ^7- Test configuration')
    print('^3/garage_help ^7- Show this help')
    print('')
    print('^2[GARAGE INFO] ^7System Information:')
    print('^3Version: ^71.0.0')
    print('^3Developer: ^7el8rbawY')
    print('^3Store: ^7HyperScript Store')
    print('^3Support: ^7https://discord.gg/EbHgD4NEzP')
end, false)

-- تشغيل اختبار تلقائي عند تحميل السكربت
Citizen.CreateThread(function()
    Wait(5000) -- انتظار 5 ثوان لضمان تحميل كل شيء
    
    print('^2[GARAGE] ^7Auto-test starting...')
    ExecuteCommand('test_garage')
    
    Wait(2000)
    ExecuteCommand('test_config')
    
    print('^2[GARAGE] ^7Auto-test completed. Use /garage_help for more commands.')
end)
