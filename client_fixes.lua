-- إصلاحات العميل للمشاكل الشائعة
-- Client-side fixes for common issues

local ESX = nil

-- تحميل ESX
Citizen.CreateThread(function()
    while ESX == nil do
        ESX = exports.es_extended:getSharedObject()
        Citizen.Wait(100)
    end
    
    print('^2[CLIENT FIXES] ^7ESX loaded successfully')
end)

-- إصلاح مشكلة Progress Bar
local function safeProgressBar(duration, text)
    local success, result = pcall(function()
        exports.pogressBar:drawBar(duration, text)
    end)
    
    if not success then
        -- استخدام بديل بسيط
        if ESX then
            ESX.ShowNotification(text)
        end
        -- انتظار المدة المطلوبة
        Citizen.Wait(duration)
    end
end

-- إصلاح مشكلة الإشعارات
local function safeNotification(type, message)
    local success = pcall(function()
        exports.OscarCounty_Notifications:showAttention(type, message)
    end)
    
    if not success then
        -- استخدام إشعارات ESX كبديل
        if ESX then
            local color = '~w~'
            if type == 'error' then
                color = '~r~'
            elseif type == 'success' then
                color = '~g~'
            elseif type == 'info' then
                color = '~b~'
            end
            ESX.ShowNotification(color .. message)
        end
    end
end

-- إصلاح مشكلة الرعاية
local function safeSponsorCheck()
    local success, result = pcall(function()
        return exports.OscarCounty_Web:method('isHaveSponsor')
    end)
    
    if success then
        return result
    else
        return false -- افتراضياً لا يوجد رعاية
    end
end

-- تصدير الدوال المصححة
exports('safeProgressBar', safeProgressBar)
exports('safeNotification', safeNotification)
exports('safeSponsorCheck', safeSponsorCheck)

-- أمر لاختبار الإصلاحات
RegisterCommand('test_client_fixes', function()
    print('^2[CLIENT FIXES] ^7Testing client fixes...')
    
    -- اختبار الإشعارات
    safeNotification('info', 'اختبار الإشعارات')
    Citizen.Wait(1000)
    
    -- اختبار شريط التقدم
    print('^3[CLIENT FIXES] ^7Testing progress bar...')
    safeProgressBar(2000, 'اختبار شريط التقدم')
    
    -- اختبار الرعاية
    local hasSponsor = safeSponsorCheck()
    print('^3[CLIENT FIXES] ^7Sponsor status: ' .. tostring(hasSponsor))
    
    safeNotification('success', 'تم اختبار جميع الإصلاحات')
end, false)

-- إصلاح مشكلة callback المفقود من جانب العميل
RegisterNetEvent('garage:test:callback')
AddEventHandler('garage:test:callback', function()
    if ESX then
        print('^3[CLIENT FIXES] ^7Testing oscar:server:getBill callback...')
        
        ESX.TriggerServerCallback('oscar:server:getBill', function(result)
            if result ~= nil then
                print('^2[CLIENT FIXES] ^7✓ Callback working! Bills: ' .. tostring(result))
                safeNotification('success', 'Callback يعمل بنجاح!')
            else
                print('^1[CLIENT FIXES] ^7✗ Callback failed!')
                safeNotification('error', 'Callback لا يعمل!')
            end
        end, 'GetBils')
    end
end)

-- معالج للأخطاء العامة
local function handleError(err)
    print('^1[CLIENT FIXES] ^7Error caught: ' .. tostring(err))
    if ESX then
        ESX.ShowNotification('~r~حدث خطأ في النظام')
    end
end

-- حماية من الأخطاء
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        
        -- التحقق من حالة ESX
        if not ESX then
            ESX = exports.es_extended:getSharedObject()
        end
    end
end)

-- أمر لإعادة تحميل ESX
RegisterCommand('reload_esx', function()
    ESX = nil
    ESX = exports.es_extended:getSharedObject()
    
    if ESX then
        print('^2[CLIENT FIXES] ^7ESX reloaded successfully')
        if ESX.PlayerData then
            print('^3[CLIENT FIXES] ^7Player job: ' .. (ESX.PlayerData.job and ESX.PlayerData.job.name or 'unknown'))
        end
    else
        print('^1[CLIENT FIXES] ^7Failed to reload ESX')
    end
end, false)

-- معلومات التصحيح المحسنة
RegisterCommand('garage_debug_enhanced', function()
    print('^2[ENHANCED DEBUG] ^7Running enhanced compatibility checks...')
    
    -- فحص ESX
    if ESX then
        print('^2[DEBUG] ^7ESX: OK')
        if ESX.PlayerData then
            print('^2[DEBUG] ^7Player Data: OK')
            print('^3[DEBUG] ^7Job: ' .. (ESX.PlayerData.job and ESX.PlayerData.job.name or 'unknown'))
            print('^3[DEBUG] ^7Identifier: ' .. (ESX.PlayerData.identifier or 'unknown'))
        else
            print('^1[DEBUG] ^7Player Data: FAILED')
        end
    else
        print('^1[DEBUG] ^7ESX: FAILED')
    end
    
    -- فحص الإشعارات
    local notifSuccess = pcall(function()
        exports.OscarCounty_Notifications:showAttention('info', 'Test notification')
    end)
    print('^' .. (notifSuccess and '2' or '1') .. '[DEBUG] ^7Notifications: ' .. (notifSuccess and 'OK' or 'FAILED'))
    
    -- فحص شريط التقدم
    local progressSuccess = pcall(function()
        exports.pogressBar:drawBar(100, 'Test progress')
    end)
    print('^' .. (progressSuccess and '2' or '1') .. '[DEBUG] ^7Progress Bar: ' .. (progressSuccess and 'OK' or 'FAILED'))
    
    -- فحص الرعاية
    local sponsorSuccess = pcall(function()
        return exports.OscarCounty_Web:method('isHaveSponsor')
    end)
    print('^' .. (sponsorSuccess and '2' or '1') .. '[DEBUG] ^7Sponsor System: ' .. (sponsorSuccess and 'OK' or 'FAILED'))
    
    -- اختبار callback
    if ESX then
        ESX.TriggerServerCallback('oscar:server:getBill', function(result)
            if result ~= nil then
                print('^2[DEBUG] ^7Callback Test: OK (Bills: ' .. tostring(result) .. ')')
            else
                print('^1[DEBUG] ^7Callback Test: FAILED')
            end
        end, 'GetBils')
    end
    
    print('^2[ENHANCED DEBUG] ^7Enhanced compatibility check completed')
end, false)

print('^2[CLIENT FIXES] ^7Client fixes loaded successfully')
