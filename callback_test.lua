-- اختبار callback للتأكد من عمله
-- Callback test to ensure it's working

if not IsDuplicityVersion() then
    -- Client-side callback test
    
    local ESX = nil
    
    Citizen.CreateThread(function()
        while ESX == nil do
            ESX = exports.es_extended:getSharedObject()
            Citizen.Wait(100)
        end
    end)
    
    -- أمر لاختبار callback التخزين
    RegisterCommand('test_storage_callback', function()
        if not ESX then
            print('^1[CALLBACK TEST] ^7ESX not loaded!')
            return
        end
        
        print('^3[CALLBACK TEST] ^7Testing storage callback...')
        
        -- اختبار مع plate وهمي
        ESX.TriggerServerCallback('HyperScript_Garage:callback-server', function(data)
            if data then
                print('^2[CALLBACK TEST] ^7✓ SUCCESS! Received data:')
                print('^3[CALLBACK TEST] ^7  - isOwner: ' .. tostring(data.isOwner))
                print('^3[CALLBACK TEST] ^7  - vehType: ' .. tostring(data.vehType))
                print('^3[CALLBACK TEST] ^7  - vehJob: ' .. tostring(data.vehJob))
                
                ESX.ShowNotification('~g~✓ Storage callback يعمل بنجاح!')
            else
                print('^1[CALLBACK TEST] ^7✗ FAILED! No data received')
                ESX.ShowNotification('~r~✗ Storage callback لا يعمل!')
            end
        end, 'isCanStorage', { plate = 'TEST123' })
    end, false)
    
    -- أمر لاختبار callback في مركبة حقيقية
    RegisterCommand('test_real_vehicle', function()
        if not ESX then
            print('^1[CALLBACK TEST] ^7ESX not loaded!')
            return
        end
        
        local playerPed = PlayerPedId()
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        
        if vehicle == 0 then
            print('^1[CALLBACK TEST] ^7You must be in a vehicle!')
            ESX.ShowNotification('~r~يجب أن تكون في مركبة!')
            return
        end
        
        local plate = GetVehicleNumberPlateText(vehicle)
        if plate then
            plate = plate:trim()
            print('^3[CALLBACK TEST] ^7Testing with real vehicle plate: ' .. plate)
            
            ESX.TriggerServerCallback('HyperScript_Garage:callback-server', function(data)
                if data then
                    print('^2[CALLBACK TEST] ^7✓ Real vehicle test SUCCESS!')
                    print('^3[CALLBACK TEST] ^7  - Plate: ' .. plate)
                    print('^3[CALLBACK TEST] ^7  - isOwner: ' .. tostring(data.isOwner))
                    print('^3[CALLBACK TEST] ^7  - vehType: ' .. tostring(data.vehType))
                    print('^3[CALLBACK TEST] ^7  - vehJob: ' .. tostring(data.vehJob))
                    
                    local ownerText = data.isOwner and 'أنت المالك' or 'لست المالك'
                    ESX.ShowNotification('~g~✓ ' .. ownerText .. ' - نوع: ' .. data.vehType)
                else
                    print('^1[CALLBACK TEST] ^7✗ Real vehicle test FAILED!')
                    ESX.ShowNotification('~r~✗ فشل في فحص المركبة!')
                end
            end, 'isCanStorage', { plate = plate })
        end
    end, false)
    
    print('^2[CALLBACK TEST] ^7Callback test commands loaded')
    print('^3[CALLBACK TEST] ^7Use: /test_storage_callback or /test_real_vehicle')
end
