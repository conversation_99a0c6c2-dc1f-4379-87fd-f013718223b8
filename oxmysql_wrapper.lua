-- Wrapper بسيط لـ oxmysql
-- Simple oxmysql wrapper

if IsDuplicityVersion() then
    -- Server-side oxmysql wrapper
    
    local oxmysql = nil
    local isReady = false
    
    -- تحميل oxmysql
    Citizen.CreateThread(function()
        Citizen.Wait(1000)
        
        if GetResourceState('oxmysql') == 'started' then
            oxmysql = exports.oxmysql
            isReady = true
            print('^2[OXMYSQL WRAPPER] ^7oxmysql loaded')
        end
    end)
    
    -- إنشاء MySQL object متوافق
    MySQL = {
        execute = function(query, params, callback)
            if not isReady or not oxmysql then
                print('^1[OXMYSQL WRAPPER] ^7oxmysql not ready')
                if callback then callback(0) end
                return
            end
            
            -- تحويل التنسيق لـ oxmysql
            local success, err = pcall(function()
                oxmysql:execute(query, params, callback or function() end)
            end)
            
            if not success then
                print('^1[OXMYSQL WRAPPER] ^7Execute error: ' .. tostring(err))
                if callback then callback(0) end
            end
        end
    }
    
    -- تصدير MySQL للاستخدام العام
    _G.MySQL = MySQL
    
    -- أمر للاختبار
    RegisterCommand('test_wrapper', function(source)
        if source == 0 then
            print('^3[OXMYSQL WRAPPER] ^7Testing wrapper...')
            print('^3[OXMYSQL WRAPPER] ^7Ready: ' .. tostring(isReady))
            
            if isReady then
                MySQL.execute('SELECT 1 as test', {}, function(result)
                    if result then
                        print('^2[OXMYSQL WRAPPER] ^7✓ Wrapper test successful')
                    else
                        print('^1[OXMYSQL WRAPPER] ^7✗ Wrapper test failed')
                    end
                end)
            end
        end
    end, true)
    
    print('^2[OXMYSQL WRAPPER] ^7oxmysql wrapper loaded')
end
