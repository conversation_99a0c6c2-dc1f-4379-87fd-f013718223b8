-- إصلاح MySQL بسيط
-- Simple MySQL fix

if IsDuplicityVersion() then
    -- Server-side simple MySQL fix
    
    -- إنشاء MySQL wrapper بسيط
    local function createMySQLWrapper()
        if GetResourceState('mysql-async') == 'started' then
            print('^2[SIMPLE MYSQL] ^7Using mysql-async')
            return {
                execute = function(query, params, callback)
                    exports['mysql-async']:mysql_execute(query, params, callback or function() end)
                end,
                fetchAll = function(query, params, callback)
                    exports['mysql-async']:mysql_fetch_all(query, params, callback or function() end)
                end,
                type = 'mysql-async'
            }
        elseif GetResourceState('oxmysql') == 'started' then
            print('^2[SIMPLE MYSQL] ^7Using oxmysql')
            return {
                execute = function(query, params, callback)
                    exports.oxmysql:execute(query, params, callback or function() end)
                end,
                fetchAll = function(query, params, callback)
                    exports.oxmysql:execute(query, params, callback or function() end)
                end,
                type = 'oxmysql'
            }
        else
            print('^1[SIMPLE MYSQL] ^7No MySQL resource found')
            return {
                execute = function(query, params, callback)
                    print('^1[SIMPLE MYSQL] ^7MySQL not available for execute')
                    if callback then callback(0) end
                end,
                fetchAll = function(query, params, callback)
                    print('^1[SIMPLE MYSQL] ^7MySQL not available for fetchAll')
                    if callback then callback({}) end
                end,
                type = 'none'
            }
        end
    end
    
    -- إنشاء MySQL wrapper
    local MySQLWrapper = createMySQLWrapper()
    
    -- تصدير wrapper للاستخدام في ملفات أخرى
    exports('getMySQL', function()
        return MySQLWrapper
    end)
    
    -- دوال مساعدة
    exports('executeQuery', function(query, params, callback)
        MySQLWrapper.execute(query, params, callback)
    end)
    
    exports('fetchAllQuery', function(query, params, callback)
        MySQLWrapper.fetchAll(query, params, callback)
    end)
    
    -- أمر للتحقق من MySQL
    RegisterCommand('test_simple_mysql', function(source)
        if source == 0 then
            print('^3[SIMPLE MYSQL] ^7Testing MySQL...')
            print('^3[SIMPLE MYSQL] ^7Type: ' .. MySQLWrapper.type)
            
            if MySQLWrapper.type ~= 'none' then
                MySQLWrapper.fetchAll('SELECT 1 as test', {}, function(result)
                    if result and (result[1] or result.test) then
                        print('^2[SIMPLE MYSQL] ^7✓ Test query successful')
                    else
                        print('^1[SIMPLE MYSQL] ^7✗ Test query failed')
                    end
                end)
            else
                print('^1[SIMPLE MYSQL] ^7✗ MySQL not available')
            end
        end
    end, true)
    
    print('^2[SIMPLE MYSQL] ^7Simple MySQL wrapper loaded (' .. MySQLWrapper.type .. ')')
end
