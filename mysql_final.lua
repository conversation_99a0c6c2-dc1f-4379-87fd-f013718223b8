-- MySQL wrapper نهائي
-- Final MySQL wrapper

if IsDuplicityVersion() then
    -- Server-side final MySQL wrapper
    
    local oxmysql = nil
    local isReady = false
    
    -- تحميل oxmysql
    Citizen.CreateThread(function()
        Citizen.Wait(2000) -- انتظار أطول
        
        if GetResourceState('oxmysql') == 'started' then
            local success, result = pcall(function()
                return exports.oxmysql
            end)
            
            if success and result then
                oxmysql = result
                isReady = true
                print('^2[MYSQL FINAL] ^7oxmysql loaded successfully')
                
                -- اختبار فوري
                local testSuccess = pcall(function()
                    oxmysql:execute('SELECT 1 as test', {}, function(result)
                        if result then
                            print('^2[MYSQL FINAL] ^7✓ Connection test successful')
                        end
                    end)
                end)
                
                if not testSuccess then
                    print('^3[MYSQL FINAL] ^7Testing alternative method...')
                    pcall(function()
                        oxmysql.execute('SELECT 1 as test', {}, function(result)
                            if result then
                                print('^2[MYSQL FINAL] ^7✓ Alternative method works')
                            end
                        end)
                    end)
                end
            else
                print('^1[MYSQL FINAL] ^7Failed to load oxmysql')
            end
        else
            print('^1[MYSQL FINAL] ^7oxmysql not started')
        end
    end)
    
    -- دالة تنفيذ نهائية
    local function finalExecute(query, params, callback)
        if not isReady or not oxmysql then
            print('^1[MYSQL FINAL] ^7MySQL not ready')
            if callback then
                Citizen.SetTimeout(100, function()
                    callback(0)
                end)
            end
            return
        end

        -- التحقق من المدخلات وإصلاحها
        if type(query) ~= 'string' then
            print('^1[MYSQL FINAL] ^7Invalid query type: ' .. type(query))
            print('^1[MYSQL FINAL] ^7Query value: ' .. json.encode(query))
            print('^1[MYSQL FINAL] ^7Params value: ' .. json.encode(params))
            print('^1[MYSQL FINAL] ^7Callback type: ' .. type(callback))

            -- محاولة تحويل إلى string إذا كان object
            if type(query) == 'table' and query.query then
                query = query.query
                print('^2[MYSQL FINAL] ^7Extracted query from object: ' .. tostring(query))
            elseif type(query) == 'table' and query[1] then
                query = query[1]
                print('^2[MYSQL FINAL] ^7Extracted query from array: ' .. tostring(query))
            else
                print('^1[MYSQL FINAL] ^7Cannot extract valid query from object')
                if callback then callback(0) end
                return
            end
        end

        if type(params) ~= 'table' then
            params = {}
        end

        -- طباعة معلومات debug (مبسط)
        -- print('^3[MYSQL FINAL] ^7Executing query: ' .. tostring(query))

        -- محاولة التنفيذ
        local success = false

        -- الطريقة الأولى
        success = pcall(function()
            oxmysql:execute(query, params, function(result)
                if callback then
                    callback(result)
                end
            end)
        end)

        if not success then
            -- الطريقة الثانية
            success = pcall(function()
                oxmysql.execute(query, params, function(result)
                    if callback then
                        callback(result)
                    end
                end)
            end)
        end

        if not success then
            print('^1[MYSQL FINAL] ^7All methods failed for query: ' .. tostring(query))
            if callback then callback(0) end
        else
            print('^2[MYSQL FINAL] ^7Query executed successfully')
        end
    end
    
    -- إنشاء MySQL global
    _G.MySQL = {
        execute = finalExecute,
        Async = {
            execute = finalExecute,
            fetchAll = finalExecute
        }
    }
    
    -- تصدير دوال
    exports('execute', finalExecute)
    exports('isReady', function() return isReady end)
    
    -- أمر للاختبار
    RegisterCommand('test_mysql_final', function(source)
        if source == 0 then
            print('^3[MYSQL FINAL] ^7Testing final MySQL...')
            print('^3[MYSQL FINAL] ^7Ready: ' .. tostring(isReady))
            
            if isReady then
                finalExecute('SELECT 1 as test', {}, function(result)
                    if result then
                        print('^2[MYSQL FINAL] ^7✓ Final test successful')
                    else
                        print('^1[MYSQL FINAL] ^7✗ Final test failed')
                    end
                end)
            end
        end
    end, true)
    
    print('^2[MYSQL FINAL] ^7Final MySQL wrapper loaded')
end
