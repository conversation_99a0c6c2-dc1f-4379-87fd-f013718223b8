# OscarCounty Garage System

## نظام كراج أوسكار كاونتي

سكربت كراج متقدم لخوادم FiveM يدعم جميع أنواع المركبات مع واجهة مستخدم حديثة.

### المميزات

- 🚗 دعم جميع أنواع المركبات (سيارات، شاحنات، قوارب، مروحيات، طائرات)
- 🏢 كراجات متخصصة للوظائف المختلفة (شرطة، إسعاف، ميكانيكي)
- 🏠 دعم كراجات المنازل الخاصة
- 🚫 نظام حجز المركبات مع رسوم الاستخراج
- 📍 بليبس على الخريطة لجميع الكراجات
- 💾 حفظ تلقائي لحالة المركبات
- 🎨 واجهة مستخدم حديثة وسهلة الاستخدام
- 📊 نظام سجلات لتتبع الاستخدام

### المتطلبات

- ESX Framework
- MySQL Database
- Node.js (للواجهة)

### التثبيت

1. **نسخ الملفات:**
   ```bash
   # انسخ المجلد إلى مجلد resources
   cp -r OscarCounty_Garage_Client [path-to-server]/resources/
   ```

2. **قاعدة البيانات:**
   ```sql
   # قم بتشغيل ملف garage.sql في قاعدة البيانات
   source garage.sql
   ```

3. **إضافة إلى server.cfg:**
   ```cfg
   ensure OscarCounty_Garage_Client
   ```

4. **إعادة تشغيل السيرفر**

### الإعدادات

يمكن تعديل الإعدادات في ملف `server.js`:

```javascript
const Config = {
    impoundedPrice: 5000,        // سعر استخراج المركبة من الحجز
    deleteVehicleOnStore: true,  // حذف المركبة عند التخزين
    enableBlips: true,           // تفعيل البليبس
    maxVehiclesPerPlayer: 50     // الحد الأقصى للمركبات
};
```

### أنواع الكراجات

#### 1. كراجات المواطنين (civ)
- متاحة لجميع اللاعبين
- تدعم السيارات والشاحنات والقوارب والمروحيات

#### 2. كراجات الوظائف
- **الشرطة (police):** مركبات الشرطة
- **الإسعاف (ambulance):** مركبات الطوارئ الطبية
- **الميكانيكي (mechanic):** مركبات الصيانة
- **أمن المنشآت (agent):** مركبات الأمن

#### 3. كراجات الحجز (impounded)
- للمركبات المحجوزة
- تتطلب دفع رسوم للاستخراج

### الاستخدام

#### للاعبين:
1. اقترب من أي كراج (ستظهر علامة على الخريطة)
2. اضغط E للتفاعل
3. اختر المركبة التي تريد إخراجها أو تخزينها
4. للمركبات المحجوزة، ادفع الرسوم المطلوبة

#### للمطورين:
```lua
-- إضافة كراج جديد
TriggerEvent('garage:addGarage', {
    type = 'car',
    job = 'civ',
    title = 'كراج جديد',
    coords = {x = 0, y = 0, z = 0},
    spawn = {x = 0, y = 0, z = 0, h = 0}
})

-- حجز مركبة
TriggerEvent('garage:impoundVehicle', plate, reason)
```

### الأحداث المتاحة

#### Client Events:
- `garage:openUI` - فتح واجهة الكراج
- `garage:closeUI` - إغلاق واجهة الكراج
- `garage:spawnVehicleClient` - إنشاء مركبة
- `garage:vehicleStored` - تأكيد تخزين المركبة

#### Server Events:
- `garage:getOwnedVehicles` - الحصول على المركبات المملوكة
- `garage:storeVehicle` - تخزين مركبة
- `garage:spawnVehicle` - إخراج مركبة
- `garage:releaseImpoundedVehicle` - استخراج مركبة محجوزة

### قاعدة البيانات

#### الجداول المستخدمة:
- `owned_vehicles` - المركبات المملوكة (محدث)
- `garage_settings` - إعدادات الكراجات
- `garage_logs` - سجلات الاستخدام
- `house_garages` - كراجات المنازل

### حل المشاكل

إذا واجهت مشكلة `Server Callback oscar:server:getBill does not exist`:
1. تأكد من وجود ملف `server.js` الجديد
2. تأكد من وجود ملف `compatibility.lua`
3. أعد تشغيل السيرفر
4. راجع ملف `TROUBLESHOOTING.md` للحلول التفصيلية

### أوامر التصحيح

في F8:
```
/garage_debug    # فحص شامل للنظام
/test_garage     # اختبار أساسي
/garage_help     # عرض المساعدة
```

### الدعم الفني

للحصول على الدعم الفني:
- Discord: https://discord.gg/EbHgD4NEzP
- المطور: el8rbawY
- دليل حل المشاكل: `TROUBLESHOOTING.md`

### الترخيص

هذا السكربت مطور بواسطة el8rbawY لـ HyperScript Store.
جميع الحقوق محفوظة.

### ملاحظات مهمة

1. تأكد من تشغيل ملف SQL قبل استخدام السكربت
2. تأكد من وجود ESX Framework
3. قم بعمل نسخة احتياطية من قاعدة البيانات قبل التثبيت
4. تأكد من صحة إعدادات قاعدة البيانات في server.cfg

### التحديثات

- v1.0.0: الإصدار الأول
- إضافة دعم كراجات المنازل
- تحسين الأداء والاستقرار
- إضافة نظام السجلات

---

**تم التطوير بواسطة el8rbawY**  
**HyperScript Store - أفضل سكربتات FiveM**
