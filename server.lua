-- ``````````` ## Development By el8rbawY ## ```````````
-- Server script for OscarCounty Garage System

local ESX = exports.es_extended:getSharedObject()

-- استخدام MySQL من wrapper
-- MySQL سيتم تعيينه من mysql_final.lua
print('^2[GARAGE] ^7Using MySQL from wrapper')

-- إعدادات السكربت
local Config = {
    impoundedPrice = 5000, -- سعر استخراج المركبة من الحجز
    deleteVehicleOnStore = true, -- حذف المركبة عند تخزينها
    enableBlips = true, -- تفعيل البليبس على الخريطة
    maxVehiclesPerPlayer = 50, -- الحد الأقصى للمركبات لكل لاعب
    
    -- أنواع المركبات المسموحة
    allowedVehicleTypes = {
        car = true,
        truck = true,
        boat = true,
        helicopter = true,
        aircraft = true
    }
}

-- دالة للحصول على معلومات اللاعب
local function getPlayerData(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then 
        return nil 
    end
    
    return {
        identifier = xPlayer.identifier,
        job = xPlayer.job.name,
        money = xPlayer.getMoney(),
        bank = xPlayer.getAccount('bank').money
    }
end

-- دالة للتحقق من صلاحية الوصول للكراج
local function hasGarageAccess(playerJob, garageJob)
    if garageJob == 'civ' then 
        return true 
    end
    if garageJob == 'impounded' then 
        return true 
    end
    return playerJob == garageJob
end

-- دالة للحصول على المركبات المملوكة
local function getOwnedVehicles(identifier, callback)
    if not _G.MySQL then
        print('^1[GARAGE ERROR] ^7MySQL not available for getOwnedVehicles')
        callback({})
        return
    end
    local MySQL = _G.MySQL

    -- استخدام oxmysql
    MySQL.execute('SELECT * FROM owned_vehicles WHERE owner = @owner', {
        ['@owner'] = identifier
    }, function(result)
        callback(result or {})
    end)
end

-- دالة لحفظ المركبة في قاعدة البيانات
local function storeVehicle(identifier, vehicleData, callback)
    if not _G.MySQL then
        print('^1[GARAGE ERROR] ^7MySQL not available for storeVehicle')
        callback(false)
        return
    end
    local MySQL = _G.MySQL

    local vehicleProps = json.encode(vehicleData.props)

    MySQL.execute('UPDATE owned_vehicles SET stored = @stored, vehicle = @vehicle WHERE owner = @owner AND plate = @plate', {
        ['@stored'] = 1,
        ['@vehicle'] = vehicleProps,
        ['@owner'] = identifier,
        ['@plate'] = vehicleData.plate
    }, function(affectedRows)
        local rowCount = type(affectedRows) == 'table' and affectedRows.affectedRows or affectedRows
        callback(rowCount and rowCount > 0)
    end)
end

-- دالة لإخراج المركبة من الكراج
local function spawnVehicle(identifier, plate, callback)
    if not _G.MySQL then
        print('^1[GARAGE ERROR] ^7MySQL not available for spawnVehicle')
        callback(nil, false)
        return
    end
    local MySQL = _G.MySQL

    MySQL.execute('SELECT * FROM owned_vehicles WHERE owner = @owner AND plate = @plate AND stored = 1', {
        ['@owner'] = identifier,
        ['@plate'] = plate
    }, function(result)
        if result and result[1] then
            MySQL.execute('UPDATE owned_vehicles SET stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@stored'] = 0,
                ['@owner'] = identifier,
                ['@plate'] = plate
            }, function(affectedRows)
                local rowCount = type(affectedRows) == 'table' and affectedRows.affectedRows or affectedRows
                callback(result[1], rowCount and rowCount > 0)
            end)
        else
            callback(nil, false)
        end
    end)
end

-- دالة للتحقق من المركبات المحجوزة
local function getImpoundedVehicles(identifier, callback)
    if not MySQL then
        print('^1[GARAGE ERROR] ^7MySQL not available for getImpoundedVehicles')
        callback({})
        return
    end

    MySQL.execute('SELECT * FROM owned_vehicles WHERE owner = @owner AND stored = 0 AND impounded = 1', {
        ['@owner'] = identifier
    }, function(result)
        callback(result or {})
    end)
end

-- دالة لاستخراج المركبة من الحجز
local function releaseImpoundedVehicle(identifier, plate, callback)
    if not MySQL then
        print('^1[GARAGE ERROR] ^7MySQL not available for releaseImpoundedVehicle')
        callback(false)
        return
    end

    MySQL.execute('UPDATE owned_vehicles SET impounded = @impounded WHERE owner = @owner AND plate = @plate', {
        ['@impounded'] = 0,
        ['@owner'] = identifier,
        ['@plate'] = plate
    }, function(affectedRows)
        local rowCount = type(affectedRows) == 'table' and affectedRows.affectedRows or affectedRows
        callback(rowCount and rowCount > 0)
    end)
end

-- الأحداث المستخدمة في السكربت الأصلي

-- الحدث الرئيسي للتعامل مع العمليات العامة
RegisterNetEvent('HyperScript_Garage:handleGeneral-server')
AddEventHandler('HyperScript_Garage:handleGeneral-server', function(action, data, extraData)
    local source = source
    local playerData = getPlayerData(source)
    
    if not playerData then 
        return 
    end
    
    if action == 'getVehicles' then
        getOwnedVehicles(playerData.identifier, function(vehicles)
            local filteredVehicles = {}
            for i = 1, #vehicles do
                local vehicle = vehicles[i]
                if data.job == 'impounded' then
                    if vehicle.impounded == 1 then
                        table.insert(filteredVehicles, vehicle)
                    end
                else
                    if vehicle.stored == 1 and vehicle.impounded == 0 then
                        table.insert(filteredVehicles, vehicle)
                    end
                end
            end
            
            TriggerClientEvent('HyperScript_Garage:handleGeneral-client', source, 'setVehicles', filteredVehicles)
        end)
        
    elseif action == 'vehStatus' then
        -- تحديث حالة المركبة (داخل/خارج الكراج)
        local plate = data.plate
        local isStored = not data.value -- إذا كانت value = false فالمركبة مخزنة
        
        if _G.MySQL then
            local MySQL = _G.MySQL
            MySQL.execute('UPDATE owned_vehicles SET stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@stored'] = isStored and 1 or 0,
                ['@owner'] = playerData.identifier,
                ['@plate'] = plate
            }, function(affectedRows)
                local rowCount = type(affectedRows) == 'table' and affectedRows.affectedRows or affectedRows
                if rowCount and rowCount > 0 then
                    print('^2[GARAGE] ^7Vehicle ' .. plate .. ' status updated: stored = ' .. tostring(isStored))
                end
            end)
        end
        
    elseif action == 'vehStatusTrailer' then
        -- تحديث حالة المقطورة
        local trailerPlate = data.Tplate
        
        if _G.MySQL then
            local MySQL = _G.MySQL
            MySQL.execute('UPDATE owned_vehicles SET stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@stored'] = 1,
                ['@owner'] = playerData.identifier,
                ['@plate'] = trailerPlate
            }, function(affectedRows)
                local rowCount = type(affectedRows) == 'table' and affectedRows.affectedRows or affectedRows
                if rowCount and rowCount > 0 then
                    print('^2[GARAGE] ^7Trailer ' .. trailerPlate .. ' stored successfully')
                end
            end)
        end
        
    elseif action == 'unImpounded' then
        -- فك حجز المركبة
        local impoundedPlate = data.plate
        local isHaveSponsor = data.isHaveSponsor
        
        if not isHaveSponsor then
            -- التحقق من وجود المال الكافي
            if playerData.bank < Config.impoundedPrice and playerData.money < Config.impoundedPrice then
                TriggerClientEvent('esx:showNotification', source, 'ليس لديك مال كافي')
                return
            end
            
            local xPlayer = ESX.GetPlayerFromId(source)
            if playerData.bank >= Config.impoundedPrice then
                xPlayer.removeAccountMoney('bank', Config.impoundedPrice)
            else
                xPlayer.removeMoney(Config.impoundedPrice)
            end
        end
        
        if _G.MySQL then
            local MySQL = _G.MySQL
            MySQL.execute('UPDATE owned_vehicles SET impounded = @impounded, stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@impounded'] = 0,
                ['@stored'] = 0,
                ['@owner'] = playerData.identifier,
                ['@plate'] = impoundedPlate
            }, function(affectedRows)
                local rowCount = type(affectedRows) == 'table' and affectedRows.affectedRows or affectedRows
                if rowCount and rowCount > 0 then
                    local message = isHaveSponsor and 'تم فك حجز المركبة مجاناً' or ('تم فك حجز المركبة مقابل $' .. Config.impoundedPrice)
                    TriggerClientEvent('esx:showNotification', source, message)
                else
                    TriggerClientEvent('esx:showNotification', source, 'فشل في فك حجز المركبة')
                end
            end)
        end
    end
end)

-- Callback للتحقق من إمكانية تخزين المركبة
ESX.RegisterServerCallback('HyperScript_Garage:callback-server', function(source, cb, action, data)
    local playerData = getPlayerData(source)
    
    if not playerData then
        cb(nil)
        return
    end
    
    if action == 'isCanStorage' then
        local plate = data and data.plate

        if not plate then
            print('^1[GARAGE ERROR] ^7No plate provided for isCanStorage')
            cb({
                isOwner = false,
                vehType = 'car',
                vehJob = 'civ'
            })
            return
        end

        -- محاولة استخدام MySQL مع معالجة الأخطاء
        local success, err = pcall(function()
            local MySQL = _G.MySQL
            MySQL.execute('SELECT * FROM owned_vehicles WHERE plate = @plate', {
                ['@plate'] = plate
            }, function(result)
                if result and result[1] then
                    local vehicle = result[1]
                    local isOwner = vehicle.owner == playerData.identifier

                    -- تحديد نوع المركبة ووظيفتها بناءً على البيانات المحفوظة
                    local vehType = vehicle.garage_type or 'car'
                    local vehJob = vehicle.job or 'civ'

                    print('^2[GARAGE] ^7Vehicle check for plate ' .. plate .. ': owner=' .. tostring(isOwner) .. ', type=' .. vehType)

                    cb({
                        isOwner = isOwner,
                        vehType = vehType,
                        vehJob = vehJob
                    })
                else
                    print('^3[GARAGE] ^7Vehicle not found in database: ' .. plate)
                    cb({
                        isOwner = false,
                        vehType = 'car',
                        vehJob = 'civ'
                    })
                end
            end)
        end)

        if not success then
            print('^1[GARAGE ERROR] ^7MySQL error: ' .. tostring(err))
            -- إرجاع قيم افتراضية في حالة خطأ MySQL
            cb({
                isOwner = true, -- السماح بالتخزين في حالة الخطأ
                vehType = 'car',
                vehJob = 'civ'
            })
        end
        
    elseif action == 'getOwnedHouses' then
        -- إرجاع قائمة فارغة إذا لم يكن هناك نظام منازل
        cb({})
        
    else
        cb(nil)
    end
end)

print('^2[GARAGE] ^7Server script loaded successfully')
