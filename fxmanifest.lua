fx_version 'cerulean'
game 'gta5'

author 'el8rbawY'
description 'HyperScript Store => https://discord.gg/EbHgD4NEzP'
version '1.0.0'

ui_page 'ui_page/build/index.html'
files { 
   'ui_page/build/*',
   'ui_page/build/static/css/*.css',
   'ui_page/build/static/js/*.js',
   'ui_page/build/static/media/*',
   'ui_page/build/images/*'
}

server_script 'oxmysql_fix.lua'
server_script 'mysql_safe.lua'
server_script 'simple_mysql.lua'
server_script 'mysql_fix.lua'
server_script 'server_callbacks.lua'
server_script 'server.lua'
shared_script 'compatibility.lua'
shared_script 'hotfix.lua'
client_script 'client_fixes.lua'
client_script 'progress_fix.lua'
client_script 'exports_fix.lua'
client_script 'client.js'

-- ملفات الاختبار (يمكن حذفها في الإنتاج)
client_script 'test.lua'
client_script 'simple_test.lua'
client_script 'callback_test.lua'

dependency 'es_extended'

-- إضافة MySQL resources كاعتماد (oxmysql مفضل)
dependencies {
    'oxmysql',
    'mysql-async',
    'ghmattimysql'
}