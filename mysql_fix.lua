-- إصلاح مشكلة MySQL
-- MySQL fix for garage system

if IsDuplicityVersion() then
    -- Server-side MySQL fix
    
    -- البحث عن MySQL resource المتاح
    local MySQL = nil
    local mysqlType = 'none'
    
    if GetResourceState('mysql-async') == 'started' then
        MySQL = exports['mysql-async']:mysql_async()
        mysqlType = 'mysql-async'
        print('^2[MYSQL FIX] ^7Using mysql-async')
    elseif GetResourceState('oxmysql') == 'started' then
        MySQL = exports.oxmysql
        mysqlType = 'oxmysql'
        print('^2[MYSQL FIX] ^7Using oxmysql')
    elseif GetResourceState('ghmattimysql') == 'started' then
        MySQL = exports.ghmattimysql
        mysqlType = 'ghmattimysql'
        print('^2[MYSQL FIX] ^7Using ghmattimysql')
    else
        print('^1[MYSQL FIX] ^7No MySQL resource found!')
        print('^3[MYSQL FIX] ^7Available resources:')
        print('^3[MYSQL FIX] ^7- mysql-async: ' .. GetResourceState('mysql-async'))
        print('^3[MYSQL FIX] ^7- oxmysql: ' .. GetResourceState('oxmysql'))
        print('^3[MYSQL FIX] ^7- ghmattimysql: ' .. GetResourceState('ghmattimysql'))
    end
    
    -- دالة آمنة لتنفيذ استعلامات MySQL
    local function safeExecute(query, params, callback)
        if not MySQL then
            print('^1[MYSQL FIX] ^7MySQL not available for execute')
            if callback then callback(0) end
            return
        end
        
        local success, err = pcall(function()
            if mysqlType == 'oxmysql' then
                MySQL:execute(query, params, callback)
            else
                MySQL.Async.execute(query, params, callback)
            end
        end)
        
        if not success then
            print('^1[MYSQL FIX] ^7Execute error: ' .. tostring(err))
            if callback then callback(0) end
        end
    end
    
    -- دالة آمنة لجلب البيانات
    local function safeFetchAll(query, params, callback)
        if not MySQL then
            print('^1[MYSQL FIX] ^7MySQL not available for fetchAll')
            if callback then callback({}) end
            return
        end
        
        local success, err = pcall(function()
            if mysqlType == 'oxmysql' then
                MySQL:execute(query, params, callback)
            else
                MySQL.Async.fetchAll(query, params, callback)
            end
        end)
        
        if not success then
            print('^1[MYSQL FIX] ^7FetchAll error: ' .. tostring(err))
            if callback then callback({}) end
        end
    end
    
    -- تصدير الدوال الآمنة
    exports('safeExecute', safeExecute)
    exports('safeFetchAll', safeFetchAll)
    exports('getMySQLType', function() return mysqlType end)
    exports('isMySQLAvailable', function() return MySQL ~= nil end)
    
    -- أمر للتحقق من حالة MySQL
    RegisterCommand('check_mysql', function(source)
        if source == 0 then -- من وحدة تحكم السيرفر
            print('^3[MYSQL FIX] ^7Checking MySQL status...')
            print('^3[MYSQL FIX] ^7Type: ' .. mysqlType)
            print('^3[MYSQL FIX] ^7Available: ' .. tostring(MySQL ~= nil))
            
            if MySQL then
                print('^2[MYSQL FIX] ^7✓ MySQL is working')
                
                -- اختبار استعلام بسيط
                safeFetchAll('SELECT 1 as test', {}, function(result)
                    if result and result[1] then
                        print('^2[MYSQL FIX] ^7✓ Test query successful')
                    else
                        print('^1[MYSQL FIX] ^7✗ Test query failed')
                    end
                end)
            else
                print('^1[MYSQL FIX] ^7✗ MySQL not available')
            end
        end
    end, true)
    
    -- إنشاء جدول owned_vehicles إذا لم يكن موجود
    Citizen.CreateThread(function()
        Citizen.Wait(5000) -- انتظار تحميل MySQL
        
        if MySQL then
            print('^3[MYSQL FIX] ^7Checking owned_vehicles table...')
            
            local createTableQuery = [[
                CREATE TABLE IF NOT EXISTS `owned_vehicles` (
                    `owner` varchar(60) NOT NULL,
                    `plate` varchar(12) NOT NULL,
                    `vehicle` longtext,
                    `stored` tinyint(1) NOT NULL DEFAULT 1,
                    `impounded` tinyint(1) NOT NULL DEFAULT 0,
                    `garage_type` varchar(50) DEFAULT 'car',
                    `job` varchar(50) DEFAULT 'civ',
                    PRIMARY KEY (`plate`),
                    KEY `owner` (`owner`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ]]
            
            safeExecute(createTableQuery, {}, function(result)
                print('^2[MYSQL FIX] ^7owned_vehicles table check completed')
            end)
            
            -- إضافة أعمدة إذا لم تكن موجودة
            safeExecute('ALTER TABLE owned_vehicles ADD COLUMN IF NOT EXISTS `impounded` tinyint(1) NOT NULL DEFAULT 0', {}, function() end)
            safeExecute('ALTER TABLE owned_vehicles ADD COLUMN IF NOT EXISTS `garage_type` varchar(50) DEFAULT "car"', {}, function() end)
            safeExecute('ALTER TABLE owned_vehicles ADD COLUMN IF NOT EXISTS `job` varchar(50) DEFAULT "civ"', {}, function() end)
        end
    end)
    
    print('^2[MYSQL FIX] ^7MySQL fix loaded')
end
