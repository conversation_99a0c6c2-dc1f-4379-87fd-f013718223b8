# دليل التثبيت السريع - OscarCounty Garage System

## خطوات التثبيت

### 1. متطلبات النظام
تأكد من وجود هذه المتطلبات قبل التثبيت:

- ✅ ESX Framework (أي إصدار حديث)
- ✅ MySQL Database
- ✅ mysql-async resource
- ✅ صلاحيات إدارية على السيرفر

### 2. تثبيت الملفات

#### أ) نسخ السكربت
```bash
# انسخ مجلد السكربت إلى مجلد resources
cp -r OscarCounty_Garage_Client [path-to-server]/resources/[garage]/
```

#### ب) إضافة إلى server.cfg
أضف هذا السطر إلى ملف `server.cfg`:
```cfg
ensure OscarCounty_Garage_Client
```

### 3. إعد<PERSON> قاعدة البيانات

#### أ) تشغيل ملف SQL
```sql
-- في phpMyAdmin أو أي أداة إدارة قواعد البيانات
source garage.sql;
```

#### ب) أو تشغيل الأوامر يدوياً:
```sql
-- تحديث جدول owned_vehicles
ALTER TABLE `owned_vehicles` 
ADD COLUMN IF NOT EXISTS `impounded` tinyint(1) NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS `garage_type` varchar(50) DEFAULT 'car';

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS `idx_owned_vehicles_impounded` ON `owned_vehicles` (`impounded`);
CREATE INDEX IF NOT EXISTS `idx_owned_vehicles_owner_stored` ON `owned_vehicles` (`owner`, `stored`);
```

### 4. التحقق من التثبيت

#### أ) تشغيل السيرفر
```bash
# تشغيل السيرفر والتحقق من عدم وجود أخطاء
./run.sh +exec server.cfg
```

#### ب) التحقق من السجلات
ابحث عن هذه الرسائل في سجل السيرفر:
```
[GARAGE CONFIG] Configuration loaded successfully
[GARAGE] Server script loaded successfully
```

### 5. الاختبار

#### أ) اختبار أساسي
1. ادخل إلى السيرفر
2. اذهب إلى أي كراج (ستجد علامات على الخريطة)
3. اضغط E للتفاعل
4. تأكد من ظهور الواجهة

#### ب) اختبار التخزين
1. احضر أي مركبة
2. اذهب إلى نقطة التخزين (علامة حمراء)
3. اضغط E وأنت في مقعد السائق
4. تأكد من تخزين المركبة

### 6. حل المشاكل الشائعة

#### مشكلة: لا تظهر البليبس على الخريطة
**الحل:**
```javascript
// في config.js
General: {
    enableBlips: true  // تأكد من أن هذا true
}
```

#### مشكلة: خطأ في قاعدة البيانات
**الحل:**
```sql
-- تأكد من وجود الأعمدة المطلوبة
DESCRIBE owned_vehicles;
```

#### مشكلة: لا تعمل الواجهة
**الحل:**
1. تأكد من وجود مجلد `ui_page/build`
2. تحقق من ملف `fxmanifest.lua`
3. أعد تشغيل السيرفر

#### مشكلة: خطأ في الأذونات
**الحل:**
```bash
# إعطاء صلاحيات للملفات
chmod -R 755 OscarCounty_Garage_Client/
```

### 7. التخصيص

#### أ) تغيير الأسعار
```javascript
// في config.js
General: {
    impoundedPrice: 10000  // غير السعر حسب الحاجة
}
```

#### ب) إضافة كراجات جديدة
```javascript
// في client.js - في مصفوفة state.garages
{
    type: 'car', 
    job: 'civ', 
    title: 'كراج جديد',
    x: 100, y: 200, z: 30, h: 0,
    spawn: { x: 105, y: 205, z: 30, h: 90 }
}
```

#### ج) تعديل الرسائل
```javascript
// في config.js
Messages: {
    success: {
        vehicleStored: 'رسالتك المخصصة هنا'
    }
}
```

### 8. الصيانة

#### أ) تنظيف قاعدة البيانات
```sql
-- حذف المركبات القديمة (اختياري)
DELETE FROM owned_vehicles WHERE stored = 1 AND DATEDIFF(NOW(), last_update) > 30;
```

#### ب) النسخ الاحتياطي
```bash
# عمل نسخة احتياطية من قاعدة البيانات
mysqldump -u username -p database_name > garage_backup.sql
```

### 9. الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من السجلات:** ابحث عن رسائل الخطأ في F8
2. **راجع الإعدادات:** تأكد من صحة ملف `config.js`
3. **اتصل بالدعم:** Discord: https://discord.gg/EbHgD4NEzP

### 10. معلومات إضافية

#### الملفات المهمة:
- `server.js` - منطق السيرفر
- `client.js` - منطق العميل
- `config.js` - الإعدادات
- `garage.sql` - قاعدة البيانات
- `fxmanifest.lua` - معلومات السكربت

#### الأحداث المهمة:
- `HyperScript_Garage:handleGeneral-server` - الحدث الرئيسي
- `HyperScript_Garage:handleGeneral-client` - استقبال البيانات

---

**تم التطوير بواسطة el8rbawY**  
**HyperScript Store - جودة عالية، أداء ممتاز**
