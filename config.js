/* ``````````` ## Development By el8rbawY ## ```````````*/
// ملف الإعدادات لسكربت كراج أوسكار كاونتي

const Config = {
    // الإعدادات العامة
    General: {
        impoundedPrice: 5000,           // سعر استخراج المركبة من الحجز
        deleteVehicleOnStore: true,     // حذف المركبة عند تخزينها
        enableBlips: true,              // تفعيل البليبس على الخريطة
        maxVehiclesPerPlayer: 50,       // الحد الأقصى للمركبات لكل لاعب
        enableHouseGarages: true,       // تفعيل كراجات المنازل
        enableJobRestrictions: true,    // تفعيل قيود الوظائف
        enableBillCheck: true,          // تفعيل فحص المخالفات
        maxBillsAllowed: 3,            // الحد الأقصى للمخالفات المسموحة
    },

    // أنواع المركبات المسموحة
    VehicleTypes: {
        car: {
            enabled: true,
            name: 'سيارات',
            marker: 36,
            blipId: 357,
            blipColor: 60
        },
        truck: {
            enabled: true,
            name: 'شاحنات',
            marker: 39,
            blipId: 477,
            blipColor: 60
        },
        boat: {
            enabled: true,
            name: 'قوارب',
            marker: 35,
            blipId: 427,
            blipColor: 0
        },
        helicopter: {
            enabled: true,
            name: 'مروحيات',
            marker: 34,
            blipId: 43,
            blipColor: 0
        },
        aircraft: {
            enabled: true,
            name: 'طائرات',
            marker: 33,
            blipId: 307,
            blipColor: 0
        }
    },

    // الوظائف المسموحة
    Jobs: {
        civ: {
            name: 'مواطن',
            allowedTypes: ['car', 'truck', 'boat', 'helicopter', 'aircraft'],
            color: [51, 153, 102, 150]
        },
        police: {
            name: 'شرطة',
            allowedTypes: ['car', 'truck', 'boat', 'helicopter', 'aircraft'],
            color: [0, 100, 200, 150]
        },
        ambulance: {
            name: 'إسعاف',
            allowedTypes: ['car', 'truck', 'helicopter'],
            color: [255, 255, 255, 150]
        },
        mechanic: {
            name: 'ميكانيكي',
            allowedTypes: ['car', 'truck'],
            color: [255, 165, 0, 150]
        },
        agent: {
            name: 'أمن المنشآت',
            allowedTypes: ['car', 'helicopter'],
            color: [128, 128, 128, 150]
        },
        impounded: {
            name: 'حجز',
            allowedTypes: ['car', 'truck', 'boat', 'helicopter', 'aircraft'],
            color: [227, 161, 28, 100]
        }
    },

    // إعدادات قاعدة البيانات
    Database: {
        enableLogs: true,               // تفعيل السجلات
        enableHouseGarageTable: true,   // تفعيل جدول كراجات المنازل
        autoCreateTables: true,         // إنشاء الجداول تلقائياً
        backupOnUpdate: false           // عمل نسخة احتياطية عند التحديث
    },

    // إعدادات الواجهة
    UI: {
        language: 'ar',                 // اللغة (ar/en)
        theme: 'dark',                  // المظهر (dark/light)
        showVehicleImages: true,        // عرض صور المركبات
        showVehicleStats: true,         // عرض إحصائيات المركبات
        enableSounds: true,             // تفعيل الأصوات
        animationSpeed: 300             // سرعة الحركة بالميلي ثانية
    },

    // إعدادات الأمان
    Security: {
        enableAntiCheat: true,          // تفعيل مكافحة الغش
        maxRequestsPerMinute: 30,       // الحد الأقصى للطلبات في الدقيقة
        enableIPLogging: false,         // تسجيل عناوين IP
        requireLicense: false           // طلب ترخيص للاستخدام
    },

    // الرسائل والإشعارات
    Messages: {
        success: {
            vehicleStored: 'تم تخزين المركبة بنجاح',
            vehicleSpawned: 'تم إخراج المركبة بنجاح',
            vehicleReleased: 'تم فك حجز المركبة',
            paymentProcessed: 'تم الدفع بنجاح'
        },
        error: {
            noMoney: 'ليس لديك مال كافي',
            notOwner: 'المركبة ليست ملكك',
            wrongGarage: 'نوع الكراج غير مناسب للمركبة',
            wrongJob: 'وظيفتك لا تسمح بالوصول لهذا الكراج',
            tooManyBills: 'يجب دفع مخالفاتك أولاً',
            notInDriverSeat: 'يجب أن تكون في مقعد السائق',
            noVehicles: 'لا توجد مركبات متاحة',
            systemError: 'حدث خطأ في النظام'
        },
        info: {
            processing: 'جاري التنفيذ...',
            loading: 'جاري التحميل...',
            connecting: 'جاري الاتصال...'
        }
    },

    // إعدادات التطوير والتصحيح
    Debug: {
        enableConsoleLog: true,         // تفعيل سجل وحدة التحكم
        enableClientDebug: false,       // تفعيل تصحيح العميل
        enableServerDebug: false,       // تفعيل تصحيح السيرفر
        logLevel: 'info',              // مستوى السجل (error/warn/info/debug)
        showPerformanceStats: false     // عرض إحصائيات الأداء
    },

    // إعدادات التكامل مع السكربتات الأخرى
    Integration: {
        // نظام الإشعارات
        notifications: {
            script: 'OscarCounty_Notifications',
            method: 'showAttention'
        },
        
        // نظام شريط التقدم
        progressBar: {
            script: 'pogressBar',
            method: 'drawBar'
        },
        
        // نظام الرعاية
        sponsor: {
            script: 'OscarCounty_Web',
            method: 'isHaveSponsor'
        },
        
        // نظام السجلات
        logs: {
            script: 'ab_Logs',
            method: 'GetAttachedVehicles'
        },
        
        // نظام الركن المتقدم
        parking: {
            script: 'AdvancedParking',
            method: 'DeleteVehicle'
        }
    }
};

// تصدير الإعدادات للاستخدام في FiveM
global.GarageConfig = Config;

console.log('^2[GARAGE CONFIG] ^7Configuration loaded successfully');
