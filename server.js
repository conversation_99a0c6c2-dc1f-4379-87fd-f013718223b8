"use strict";
/* ``````````` ## Development By el8rbawY ## ```````````*/

const ESX = exports.es_extended.getSharedObject();

// إعدادات السكربت
const Config = {
    impoundedPrice: 5000, // سعر استخراج المركبة من الحجز
    deleteVehicleOnStore: true, // حذف المركبة عند تخزينها
    enableBlips: true, // تفعيل البليبس على الخريطة
    maxVehiclesPerPlayer: 50, // الحد الأقصى للمركبات لكل لاعب
    
    // أنواع المركبات المسموحة
    allowedVehicleTypes: {
        car: true,
        truck: true,
        boat: true,
        helicopter: true,
        aircraft: true
    }
};

// دالة للحصول على معلومات اللاعب
function getPlayerData(source) {
    const xPlayer = ESX.GetPlayerFromId(source);
    if (!xPlayer) return null;
    
    return {
        identifier: xPlayer.identifier,
        job: xPlayer.job.name,
        money: xPlayer.getMoney(),
        bank: xPlayer.getAccount('bank').money
    };
}

// دالة للتحقق من صلاحية الوصول للكراج
function hasGarageAccess(playerJob, garageJob) {
    if (garageJob === 'civ') return true;
    if (garageJob === 'impounded') return true;
    return playerJob === garageJob;
}

// دالة للحصول على المركبات المملوكة
function getOwnedVehicles(identifier, callback) {
    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner', {
        ['@owner'] = identifier
    }, function(result) {
        callback(result);
    });
}

// دالة لحفظ المركبة في قاعدة البيانات
function storeVehicle(identifier, vehicleData, callback) {
    const vehicleProps = JSON.stringify(vehicleData.props);
    
    MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored, vehicle = @vehicle WHERE owner = @owner AND plate = @plate', {
        ['@stored'] = 1,
        ['@vehicle'] = vehicleProps,
        ['@owner'] = identifier,
        ['@plate'] = vehicleData.plate
    }, function(affectedRows) {
        callback(affectedRows > 0);
    });
}

// دالة لإخراج المركبة من الكراج
function spawnVehicle(identifier, plate, callback) {
    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND plate = @plate AND stored = 1', {
        ['@owner'] = identifier,
        ['@plate'] = plate
    }, function(result) {
        if (result[1]) {
            MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@stored'] = 0,
                ['@owner'] = identifier,
                ['@plate'] = plate
            }, function(affectedRows) {
                callback(result[1], affectedRows > 0);
            });
        } else {
            callback(null, false);
        }
    });
}

// دالة للتحقق من المركبات المحجوزة
function getImpoundedVehicles(identifier, callback) {
    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND stored = 0 AND impounded = 1', {
        ['@owner'] = identifier
    }, function(result) {
        callback(result);
    });
}

// دالة لاستخراج المركبة من الحجز
function releaseImpoundedVehicle(identifier, plate, callback) {
    MySQL.Async.execute('UPDATE owned_vehicles SET impounded = @impounded WHERE owner = @owner AND plate = @plate', {
        ['@impounded'] = 0,
        ['@owner'] = identifier,
        ['@plate'] = plate
    }, function(affectedRows) {
        callback(affectedRows > 0);
    });
}

// الأحداث المستخدمة في السكربت الأصلي

// الحدث الرئيسي للتعامل مع العمليات العامة
RegisterNetEvent('HyperScript_Garage:handleGeneral-server');
AddEventHandler('HyperScript_Garage:handleGeneral-server', function(action, data, extraData) {
    const source = source;
    const playerData = getPlayerData(source);

    if (!playerData) return;

    switch (action) {
        case 'getVehicles':
            getOwnedVehicles(playerData.identifier, function(vehicles) {
                const filteredVehicles = vehicles.filter(vehicle => {
                    if (data.job === 'impounded') {
                        return vehicle.impounded === 1;
                    } else {
                        return vehicle.stored === 1 && vehicle.impounded === 0;
                    }
                });

                TriggerClientEvent('HyperScript_Garage:handleGeneral-client', source, 'setVehicles', filteredVehicles);
            });
            break;

        case 'vehStatus':
            // تحديث حالة المركبة (داخل/خارج الكراج)
            const plate = data.plate;
            const isStored = !data.value; // إذا كانت value = false فالمركبة مخزنة

            MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@stored'] = isStored ? 1 : 0,
                ['@owner'] = playerData.identifier,
                ['@plate'] = plate
            }, function(affectedRows) {
                if (affectedRows > 0) {
                    console.log(`Vehicle ${plate} status updated: stored = ${isStored}`);
                }
            });
            break;

        case 'vehStatusTrailer':
            // تحديث حالة المقطورة
            const trailerPlate = data.Tplate;

            MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@stored'] = 1,
                ['@owner'] = playerData.identifier,
                ['@plate'] = trailerPlate
            }, function(affectedRows) {
                if (affectedRows > 0) {
                    console.log(`Trailer ${trailerPlate} stored successfully`);
                }
            });
            break;

        case 'unImpounded':
            // فك حجز المركبة
            const impoundedPlate = data.plate;
            const isHaveSponsor = data.isHaveSponsor;

            if (!isHaveSponsor) {
                // التحقق من وجود المال الكافي
                if (playerData.bank < Config.impoundedPrice && playerData.money < Config.impoundedPrice) {
                    TriggerClientEvent('esx:showNotification', source, 'ليس لديك مال كافي');
                    return;
                }

                const xPlayer = ESX.GetPlayerFromId(source);
                if (playerData.bank >= Config.impoundedPrice) {
                    xPlayer.removeAccountMoney('bank', Config.impoundedPrice);
                } else {
                    xPlayer.removeMoney(Config.impoundedPrice);
                }
            }

            MySQL.Async.execute('UPDATE owned_vehicles SET impounded = @impounded, stored = @stored WHERE owner = @owner AND plate = @plate', {
                ['@impounded'] = 0,
                ['@stored'] = 0,
                ['@owner'] = playerData.identifier,
                ['@plate'] = impoundedPlate
            }, function(affectedRows) {
                if (affectedRows > 0) {
                    const message = isHaveSponsor ? 'تم فك حجز المركبة مجاناً' : `تم فك حجز المركبة مقابل $${Config.impoundedPrice}`;
                    TriggerClientEvent('esx:showNotification', source, message);
                } else {
                    TriggerClientEvent('esx:showNotification', source, 'فشل في فك حجز المركبة');
                }
            });
            break;
    }
});

// Callback للتحقق من إمكانية تخزين المركبة
ESX.RegisterServerCallback('HyperScript_Garage:callback-server', function(source, cb, action, data) {
    const playerData = getPlayerData(source);

    if (!playerData) {
        cb(null);
        return;
    }

    switch (action) {
        case 'isCanStorage':
            const plate = data.plate;

            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE plate = @plate', {
                ['@plate'] = plate
            }, function(result) {
                if (result[1]) {
                    const vehicle = result[1];
                    const isOwner = vehicle.owner === playerData.identifier;

                    // تحديد نوع المركبة ووظيفتها بناءً على البيانات المحفوظة
                    let vehType = 'car';
                    let vehJob = 'civ';

                    if (vehicle.garage_type) {
                        vehType = vehicle.garage_type;
                    }

                    // يمكن إضافة منطق لتحديد الوظيفة بناءً على نوع المركبة
                    if (vehicle.job) {
                        vehJob = vehicle.job;
                    }

                    cb({
                        isOwner: isOwner,
                        vehType: vehType,
                        vehJob: vehJob
                    });
                } else {
                    cb({
                        isOwner: false,
                        vehType: 'unknown',
                        vehJob: 'unknown'
                    });
                }
            });
            break;

        case 'getOwnedHouses':
            // إرجاع قائمة فارغة إذا لم يكن هناك نظام منازل
            cb([]);
            break;

        default:
            cb(null);
    }
});

// Callback للحصول على المخالفات - الإصدار المصحح
ESX.RegisterServerCallback('oscar:server:getBill', function(source, cb, action) {
    const xPlayer = ESX.GetPlayerFromId(source);

    if (!xPlayer) {
        cb(0);
        return;
    }

    // إرجاع 0 مخالفات افتراضياً لتجنب الأخطاء
    // يمكن تخصيص هذا لاحقاً حسب نظام المخالفات المستخدم
    cb(0);

    console.log(`^3[GARAGE] ^7Bill check for player ${xPlayer.identifier}: 0 bills`);
});

// Callback إضافي للتوافق مع أنظمة المخالفات المختلفة
ESX.RegisterServerCallback('esx_billing:getBills', function(source, cb) {
    const xPlayer = ESX.GetPlayerFromId(source);

    if (!xPlayer) {
        cb([]);
        return;
    }

    // محاولة الحصول على المخالفات من جدول esx_billing
    MySQL.Async.fetchAll('SELECT * FROM billing WHERE identifier = @identifier AND paid = 0', {
        ['@identifier'] = xPlayer.identifier
    }, function(bills) {
        cb(bills || []);
    });
});

// إضافة معالج للأخطاء في حالة عدم وجود جدول المخالفات
process.on('unhandledRejection', (reason, promise) => {
    if (reason && reason.message && reason.message.includes('billing')) {
        console.log('^3[GARAGE WARNING] ^7Billing table not found, using default values');
    }
});

// Callback بديل للأنظمة التي لا تستخدم نظام مخالفات
ESX.RegisterServerCallback('garage:checkPlayerStatus', function(source, cb) {
    const xPlayer = ESX.GetPlayerFromId(source);

    if (!xPlayer) {
        cb({ bills: 0, canUseGarage: true });
        return;
    }

    // إرجاع حالة افتراضية تسمح باستخدام الكراج
    cb({
        bills: 0,
        canUseGarage: true,
        playerName: xPlayer.getName(),
        job: xPlayer.job.name
    });
});

console.log('^2[GARAGE] ^7Server script loaded successfully');
