-- إصلاح سريع لمشكلة oscar:server:getBill
-- Quick hotfix for oscar:server:getBill issue

if IsDuplicityVersion() then
    -- Server-side hotfix
    
    -- التأكد من تحميل ESX
    local ESX = exports.es_extended:getSharedObject()
    
    if ESX then
        print('^2[HOTFIX] ^7ESX loaded, registering missing callback...')
        
        -- تسجيل callback المفقود
        ESX.RegisterServerCallback('oscar:server:getBill', function(source, cb, action)
            local xPlayer = ESX.GetPlayerFromId(source)
            
            if not xPlayer then
                print('^1[HOTFIX] ^7Player not found for source: ' .. tostring(source))
                cb(0)
                return
            end
            
            -- إرجاع 0 مخالفات دائماً لتجنب الأخطاء
            cb(0)
            print('^2[HOTFIX] ^7Bill check completed for player: ' .. xPlayer.identifier .. ' (0 bills)')
        end)
        
        print('^2[HOTFIX] ^7oscar:server:getBill callback registered successfully!')
        
        -- تسجيل callback إضافي للتوافق
        ESX.RegisterServerCallback('GetBils', function(source, cb)
            cb(0)
        end)
        
    else
        print('^1[HOTFIX] ^7ESX not found! Cannot register callback.')
    end
    
    -- معالج للتحقق من نجاح الإصلاح
    AddEventHandler('onResourceStart', function(resourceName)
        if GetCurrentResourceName() == resourceName then
            Citizen.Wait(2000) -- انتظار لضمان تحميل كل شيء
            
            if ESX and ESX.ServerCallbacks and ESX.ServerCallbacks['oscar:server:getBill'] then
                print('^2[HOTFIX SUCCESS] ^7oscar:server:getBill callback is now available!')
            else
                print('^1[HOTFIX FAILED] ^7oscar:server:getBill callback still missing!')
            end
        end
    end)
    
else
    -- Client-side hotfix
    
    -- التحقق من وجود ESX
    Citizen.CreateThread(function()
        while not ESX do
            ESX = exports.es_extended:getSharedObject()
            Citizen.Wait(100)
        end
        
        print('^2[HOTFIX CLIENT] ^7ESX loaded on client side')
        
        -- اختبار callback
        Citizen.Wait(5000)
        
        ESX.TriggerServerCallback('oscar:server:getBill', function(result)
            if result ~= nil then
                print('^2[HOTFIX TEST] ^7Callback test successful, result: ' .. tostring(result))
            else
                print('^1[HOTFIX TEST] ^7Callback test failed!')
            end
        end, 'test')
    end)
    
    -- أمر لاختبار الإصلاح
    RegisterCommand('test_hotfix', function()
        if ESX then
            print('^3[HOTFIX TEST] ^7Testing oscar:server:getBill callback...')
            
            ESX.TriggerServerCallback('oscar:server:getBill', function(result)
                if result ~= nil then
                    print('^2[HOTFIX TEST] ^7✓ Callback working! Bills: ' .. tostring(result))
                    ESX.ShowNotification('~g~Hotfix working! Bills: ' .. tostring(result))
                else
                    print('^1[HOTFIX TEST] ^7✗ Callback failed!')
                    ESX.ShowNotification('~r~Hotfix failed!')
                end
            end, 'GetBils')
        else
            print('^1[HOTFIX TEST] ^7ESX not loaded!')
        end
    end, false)
    
end

-- رسالة تأكيد التحميل
print('^2[HOTFIX] ^7Hotfix loaded for oscar:server:getBill issue')
