"use strict";
/* ``````````` ## Development By el8rbawY ## ```````````*/
const state = {
    impoundedPrice: 5000, // and edit in server
    blips: [
        { id: 357, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 834.3612, y: -1342.0880, z: 26.3624 } },
        { id: 357, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 211.5100, y: -1451.6355, z: 29.2697 } },
        { id: 357, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 1727.5234, y: 3711.0820, z: 34.2496 } },
        { id: 357, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 65.7622, y: 6580.0659, z: 31.2557 } },
        { id: 357, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: -485.5865, y: -595.9509, z: 30.6750 } },
        { id: 357, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 89.7972, y: -2702.6428, z: 6.0036 } },
        { id: 357, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 1054.5085, y: 2709.0564, z: 38.6725 } },
        { id: 477, title: 'ﺕﺎﻨﺣﺎﺸﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 898.2243, y: -1552.8496, z: 30.7660 } },
        { id: 477, title: 'ﺕﺎﻨﺣﺎﺸﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 202.0433, y: 2785.9443, z: 45.6566 } },
        { id: 477, title: 'ﺕﺎﻨﺣﺎﺸﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 114.8298, y: 6550.8013, z: 31.2557 } },
        { id: 477, title: 'ﺕﺎﻨﺣﺎﺸﻟﺍ ﺝﺍﺮﻛ', color: 60, coords: { x: 459.2577, y: -663.7051, z: 27.5879 } },
        { id: 427, title: 'ﺏﺭﺍﻮﻘﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: -775.9727, y: -1494.7444, z: 2.4211 } },
        { id: 427, title: 'ﺏﺭﺍﻮﻘﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: 46.1374, y: -2757.6292, z: 5.9894 } },
        { id: 427, title: 'ﺏﺭﺍﻮﻘﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: -1610.8971, y: 5259.0942, z: 3.9741 } },
        { id: 427, title: 'ﺏﺭﺍﻮﻘﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: 1684.7825, y: 4041.3235, z: 35.7345 } },
        { id: 43, title: 'ﺕﺎﻴﺣﻭﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: -1016.8640, y: -3506.2617, z: 14.1434 } },
        { id: 43, title: 'ﺕﺎﻴﺣﻭﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: 1770.1626, y: 3239.7393, z: 42.1251 } },
        { id: 43, title: 'ﺕﺎﻴﺣﻭﺮﻤﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: -745.3396, y: -1468.6569, z: 4.9961 } },
        { id: 307, title: 'ﺕﺍﺮﺋﺎﻄﻟﺍ ﺝﺍﺮﻛ', color: 0, coords: { x: -1072.0404, y: -3499.1912, z: 14.1434 } }, 
        { id: 526, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺰﺠﺣ', color: 47, coords: { x: 1464.6281, y: 3580.7532, z: 35.6776 } },
        { id: 526, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺰﺠﺣ', color: 47, coords: { x: 422.8889, y: -1638.8970, z: 29.2932 } },
        { id: 526, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺰﺠﺣ', color: 47, coords: { x: -310.4132, y: -1162.0299, z: 23.3759 } },
        { id: 526, title: 'ﺕﺎﺒﻛﺮﻤﻟﺍ ﺰﺠﺣ', color: 47, coords: { x: 43.4882, y: 6561.8096, z: 31.2557 } },
        { id: 43, title: 'ﺕﺎﻴﺣﻭﺮﻤﻟﺍ ﺰﺠﺣ', color: 0, coords: { x: -1032.3120, y: -3535.5906, z: 14.1434 } },
        { id: 43, title: 'ﺕﺎﻴﺣﻭﺮﻤﻟﺍ ﺰﺠﺣ', color: 0, coords: { x: 1749.5874, y: 3236.6086, z: 41.9592 } },
        { id: 307, title: 'ﺕﺍﺮﺋﺎﻄﻟﺍ ﺰﺠﺣ', color: 0, coords: { x: -1057.9464, y: -3508.7742, z: 14.1434 } },
        { id: 427, title: 'ﺏﺭﺍﻮﻘﻟﺍ ﺰﺠﺣ', color: 17, coords: { x: 1659.3352, y: 4006.9753, z: 35.7362 } },
        { id: 477, title: 'ﺕﺎﻨﺣﺎﺸﻟﺍ ﺰﺠﺣ', color: 0, coords: { x: 122.8196, y: 6543.1479, z: 31.2557 } },
    ],
    // التخزين
    storages: [
        { type: 'car', job: 'civ', x: 823.3430, y: -1374.2875, z: 25.8374 },
        { type: 'car', job: 'civ', x: 420.3128, y: -1641.9908, z: 29.2920 },
        { type: 'car', job: 'civ', x: 832.7225, y: -1374.2875, z: 25.8374 },
        { type: 'car', job: 'civ', x: 862.7554, y: -1357.8590, z: 25.8374 },
        { type: 'car', job: 'civ', x: 862.5892, y: -1342.7445, z: 25.8374 },
        { type: 'car', job: 'civ', x: 208.2242, y: -1465.5571, z: 28.9500 },
        { type: 'car', job: 'civ', x: 204.1761, y: -1470.3809, z: 28.9500 },
        { type: 'car', job: 'civ', x: 190.2662, y: -1453.9169, z: 28.9500 },
        { type: 'car', job: 'civ', x: 1722.7451, y: 3713.6497, z: 33.9095 },
        { type: 'car', job: 'civ', x: 1737.6884, y: 3719.4058, z: 33.9095 },
        { type: 'car', job: 'civ', x: 69.1234, y: 6587.7031, z: 30.5557 },
        { type: 'car', job: 'civ', x: 63.8728, y: 6582.3701, z: 30.5557 },
        { type: 'car', job: 'civ', x: 58.5896, y: 6576.9004, z: 30.5557 },
        { type: 'car', job: 'police', x: 569.8546, y: 4.9426, z: 70.1131 },
        { type: 'car', job: 'police', x: 561.4034, y: 17.5325, z: 70.1131 },
        { type: 'car', job: 'police', x: 621.4583, y: 26.2429, z: 87.7750 },
        { type: 'car', job: 'civ', x: 822.7264, y: -1257.6954, z: 25.8485 },
        { type: 'car', job: 'police', x: 1861.1688, y: 3700.2717, z: 33.3746 },
        { type: 'car', job: 'agent', x: 1851.6163, y: 3693.8887, z: 33.4747 },
        { type: 'car', job: 'police', x: -472.3086, y: 6035.4507, z: 30.7405 },
        { type: 'car', job: 'police', x: 2555.7007, y: -409.4802, z: 92.5934 },
        { type: 'car', job: 'police', x: 2552.2241, y: -392.3399, z: 92.5933 },
        { type: 'car', job: 'agent', x: 870.5884, y: -2940.2563, z: 5.5016 },
        { type: 'car', job: 'agent', x: 261.5343, y: -334.9178, z: 44.3196 },
        { type: 'car', job: 'mechanic', x: -365.8638, y: -109.1873, z: 38.6969 },
        { type: 'car', job: 'ambulance', x: -291.6007, y: -637.7404, z: 32.1741 },
        { type: 'car', job: 'ambulance', x: 299.4476, y: -1441.0869, z: 29.7930 },
        { type: 'car', job: 'civ', x: 221.5066, y: 2782.1804, z: 44.8566 },
        { type: 'car', job: 'civ', x: 220.0656, y: 2791.3345, z: 44.8566 },
        { type: 'car', job: 'ambulance', x: 1159.6772, y: -1504.3715, z: 34.6926 },
        { type: 'car', job: 'ambulance', x: -253.1069, y: 6347.9263, z: 32.3302 },
        { type: 'car', job: 'ambulance', x: 1089.3480, y: 2705.6514, z: 37.8723 },
        { type: 'car', job: 'mechanic', x: 90.2904, y: 6546.4028, z: 30.7557 },
        { type: 'car', job: 'mechanic', x: 188.0168, y: 3210.1416, z: 41.9235 },
        { type: 'car', job: 'civ', x: 89.7972, y: -2702.6428, z: 6.0036 },
        { type: 'car', job: 'civ', x: -485.5865, y: -595.9509, z: 30.6750 },
        { type: 'car', job: 'civ', x: 465.8363, y: -578.1174, z: 28.4998 },
        { type: 'car', job: 'civ', x: 473.0253, y: -1018.7521, z: 28.1016 },
        { type: 'car', job: 'civ', x: 4361.8657, y: -4562.9141, z: 3.8076 },
        { type: 'car', job: 'civ', x: 1058.4637, y: 2706.0598, z: 37.9724 },
        { type: 'car', job: 'civ', x: 1050.8134, y: 2705.9573, z: 37.9724 },
        { type: 'car', job: 'civ', x: 858.9294, y: -1540.1008, z: 29.8841 },
        { type: 'car', job: 'civ', x: 863.1461, y: -1537.0266, z: 29.8841 },
        { type: 'truck', job: 'civ', x: 907.7268, y: -1564.0513, z: 30.4835 },
        { type: 'truck', job: 'civ', x: 909.2591, y: -1556.6697, z: 30.4835 },
        { type: 'truck', job: 'civ', x: 909.8588, y: -1548.8491, z: 30.4835 },
        { type: 'truck', job: 'civ', x: 909.8022, y: -1541.3635, z: 30.4835 },
        { type: 'truck', job: 'civ', x: 909.6268, y: -1533.6411, z: 30.4835 },
        { type: 'truck', job: 'civ', x: 909.2701, y: -1526.3595, z: 30.4835 },
        { type: 'truck', job: 'civ', x: 463.8728, y: -606.8218, z: 28.4997 },
        { type: 'truck', job: 'civ', x: 463.1587, y: -613.8967, z: 28.4997 },
        { type: 'truck', job: 'civ', x: 462.5026, y: -620.4897, z: 28.4997 },
        { type: 'truck', job: 'civ', x: 471.4942, y: -578.4051, z: 28.4997 },
        { type: 'truck', job: 'civ', x: 218.9428, y: 2759.4912, z: 42.7277 },
        { type: 'truck', job: 'civ', x: 204.5936, y: 2758.7668, z: 42.7277 },
        { type: 'truck', job: 'civ', x: 196.0663, y: 2755.2473, z: 42.7277 },
        { type: 'truck', job: 'civ', x: 115.9194, y: 6551.9727, z: 30.7556 },
        { type: 'truck', job: 'civ', x: 123.8568, y: 6544.4814, z: 30.7557 },
        { type: 'boat', job: 'civ', x: -801.2723, y: -1487.6593, z: 1.1755 },
        { type: 'boat', job: 'civ', x: 58.0674, y: -2776.8159, z: 1.3437 },
        { type: 'boat', job: 'civ', x: -1600.5455, y: 5259.0483, z: 1.4541 },
        { type: 'boat', job: 'civ', x: 1692.8137, y: 4044.4387, z: 31.7621 },
        { type: 'boat', job: 'civ', x: 1662.8286, y: 4022.8960, z: 31.5306 },
        { type: 'helicopter', job: 'civ', x: -1016.8640, y: -3506.2617, z: 14.1434 },
        { type: 'helicopter', job: 'civ', x: -745.3396, y: -1468.6569, z: 4.9961 },
        { type: 'helicopter', job: 'civ', x: 1770.1626, y: 3239.7393, z: 42.1251 },
        { type: 'helicopter', job: 'police', x: -475.1967, y: 5988.6499, z: 30.8367 },
        { type: 'helicopter', job: 'agent', x: 834.2668, y: -2913.8323, z: 7.7486 },
        { type: 'helicopter', job: 'civ', x: -1072.0404, y: -3499.1912, z: 14.1434 },
        { type: 'helicopter', job: 'civ', x: 1692.3444, y: 3247.3853, z: 40.8872 },
        { type: 'car', job: 'agent', x: -2985.1128, y: 2690.5964, z: 8.6892 },
        { type: 'car', job: 'agent', x: -400.7331, y: 6161.3291, z: 30.8781 },
        { type: 'car', job: 'agent', x: 768.2089, y: -2972.7290, z: 4.7286 },
    ],
    // الكراجات
    garages: [
        // --------------- المواطنين
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 823.3430, y: -1374.2875, z: 25.8374, h: 359.0085, spawn: { x: 823.3070, y: -1371.8209, z: 26.1374, h: 359.6377 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 1058.4637, y: 2706.0598, z: 37.9724, h: 355.6318, spawn: { x: 1058.5255, y: 2706.9636, z: 38.6724, h: 356.3090 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 1050.8134, y: 2705.9573, z: 37.9724, h: 357.5739, spawn: { x: 1050.8444, y: 2707.0610, z: 38.6724, h: 358.5437 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 832.7225, y: -1374.2875, z: 25.8374, h: 357.9006, spawn: { x: 832.7117, y: -1372.8477, z: 26.1332, h: 1.2990 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 862.7554, y: -1357.8590, z: 25.8374, h: 92.7080, spawn: { x: 859.4277, y: -1357.9275, z: 26.0953, h: 90.7987 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 862.5892, y: -1342.7445, z: 25.8374, h: 92.6237, spawn: { x: 859.4438, y: -1342.7961, z: 26.0321, h: 91.2617 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 208.2242, y: -1465.5571, z: 28.9500, h: 46.6160, spawn: { x: 207.2964, y: -1464.6996, z: 29.1563, h: 46.9766 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 204.1761, y: -1470.3809, z: 28.9500, h: 47.5185, spawn: { x: 202.0807, y: -1469.2542, z: 29.1414, h: 49.6608 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 190.2662, y: -1453.9169, z: 28.9500, h: 226.2401, spawn: { x: 191.4501, y: -1454.9999, z: 29.1416, h: 227.8178 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 1722.7451, y: 3713.6497, z: 33.9095, h: 21.1356, spawn: { x: 1722.7451, y: 3713.6497, z: 34.2095, h: 21.1356 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 1737.6884, y: 3719.4058, z: 33.9095, h: 21.2376, spawn: { x: 1737.6884, y: 3719.4058, z: 34.0400, h: 21.2376 },
        },
        // {
        //     type: 'car', job: 'civ', title: 'كراج المركبات',
        //     x: 66.1527, y: 6586.9824, z: 30.8817, h: 226.5133, spawn: { x: 68.8790, y: 6584.3306, z: 31.4079, h: 223.3170 }, 
        // },
        // {
        //     type: 'car', job: 'civ', title: 'كراج المركبات',
        //     x: 60.8046, y: 6581.8146, z: 30.8817, h: 224.9189, spawn: { x: 63.6155, y: 6579.0967, z: 31.4076, h: 222.6678 },
        // },
        // {
        //     type: 'car', job: 'civ', title: 'كراج المركبات',
        //     x: 55.4362, y: 6576.6177, z: 30.8817, h: 224.0691, spawn: { x: 58.2525, y: 6573.9233, z: 31.4077, h: 222.7848 },
        // },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: -485.5865, y: -595.9509, z: 30.6750, h: 180.2877, spawn: { x: -485.6287, y: -597.1210, z: 31.1750, h: 178.4218 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 89.7972, y: -2702.6428, z: 5.7036, h: 178.3314, spawn: { x: 89.7972, y: -2702.6428, z: 6.0036, h: 178.3314 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 465.8363, y: -578.1174, z: 28.4998, h: 170.1535, spawn: { x: 465.4794, y: -583.4908, z: 28.4998, h: 170.8650 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 1875.1165, y: 2581.3687, z: 45.2720, h: 91.1607, spawn: { x: 1876.6586, y: 2581.3459, z: 45.6720, h: 269.7386 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 221.5066, y: 2782.1804, z: 44.8566, h: 100.2418, spawn: { x: 217.6237, y: 2781.3508, z: 45.6566, h: 101.9554 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 220.0656, y: 2791.3345, z: 44.8566, h: 93.5316, spawn: { x: 216.3648, y: 2791.0476, z: 45.6566, h: 97.2968 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 69.1234, y: 6587.7031, z: 30.5557, h: 221.9326, spawn: { x: 70.1484, y: 6586.6030, z: 31.2557, h: 225.2695 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 63.8728, y: 6582.3701, z: 30.5557, h: 230.2630, spawn: { x: 64.9505, y: 6581.2832, z: 31.2557, h: 224.1450 },
        },
        {
            type: 'car', job: 'civ', title: 'كراج المركبات',
            x: 58.5896, y: 6576.9004, z: 30.5557, h: 226.1860, spawn: { x: 59.5282, y: 6575.5796, z: 31.2557, h: 223.3117 },
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 924.0906, y: -1563.9518, z: 30.4255, h: 89.6405, spawn: { x: 909.8285, y: -1564.1172, z: 30.7801, h: 89.3061 }
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 924.0741, y: -1556.3121, z: 30.4255, h: 86.1973, spawn: { x: 909.0529, y: -1556.2505, z: 30.7020, h: 88.0878 }
        },
        {
            type: 'truck', job: 'civ', title: 'حجز الشاحنات',
            x: 924.1664, y: -1548.5071, z: 30.4757, h: 85.2363, spawn: { x: 911.0846, y: -1548.8152, z: 30.6795, h: 88.3562 }
        },
        {
            type: 'truck', job: 'civ', title: 'حجز الشاحنات',
            x: 924.2432, y: -1541.2594, z: 30.4757, h: 86.9879, spawn: { x: 910.6713, y: -1541.2262, z: 30.6405, h: 86.8024 }
        },
        // {
        //     type: 'truck', job: 'civ', title: 'كراج الشاحنات',
        //     x: 124.7220, y: 6545.1582, z: 30.7506, h: 133.6930, spawn: { x: 117.9126, y: 6538.2461, z: 31.2607, h: 134.8302 }
        // },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 459.6344, y: -600.9086, z: 28.4997, h: 206.2605, spawn: { x: 468.8577, y: -615.3786, z: 28.4995, h: 171.6288 }
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 115.9194, y: 6551.9727, z: 30.7556, h: 136.6496, spawn: { x: 109.6524, y: 6545.5913, z: 31.2557, h: 134.1602 }
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 458.5101, y: -607.0043, z: 28.4998, h: 209.7794, spawn: { x: 465.5985, y: -638.1462, z: 28.4995, h: 172.6487 }
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 471.4942, y: -578.4051, z: 28.4997, h: 172.6799, spawn: { x: 473.9819, y: -588.8481, z: 28.4996, h: 174.0338 }
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 194.9105, y: 2763.7258, z: 42.7277, h: 188.8303, spawn: { x: 196.6388, y: 2753.9390, z: 43.0808, h: 185.6889 }
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 203.7189, y: 2765.2527, z: 42.7277, h: 186.0564, spawn: { x: 205.8031, y: 2754.7466, z: 43.4277, h: 192.9985 }
        },
        {
            type: 'truck', job: 'civ', title: 'كراج الشاحنات',
            x: 216.5737, y: 2766.0798, z: 42.7277, h: 195.6250, spawn: { x: 218.3228, y: 2755.2559, z: 43.0812, h: 193.0490 }
        },
        {
            type: 'boat', job: 'civ', title: 'كراج القوارب',
            x: -794.9193, y: -1510.9023, z: 1.5955, h: 108.1640, spawn: { x: -797.3044, y: -1502.9792, z: 1.1755, h: 109.0463 }
        },
        {
            type: 'boat', job: 'civ', title: 'كراج القوارب',
            x: 45.9295, y: -2778.7490, z: 5.7237, h: 176.8163, spawn: { x: 35.1968, y: -2780.2234, z: 2.3637, h: 177.7131 }
        },
        {
            type: 'boat', job: 'civ', title: 'كراج القوارب',
            x: -800.4221, y: -1494.6179, z: 1.5956, h: 106.9455, spawn: { x: -801.2723, y: -1487.6593, z: 1.1755, h: 102.9222 }
        },
        {
            type: 'boat', job: 'civ', title: 'كراج القوارب',
            x: -1609.1176, y: 5259.6226, z: 3.9741, h: 20.1459, spawn: { x: -1600.5455, y: 5259.0483, z: 1.4541, h: 19.3707 }
        },
        {
            type: 'boat', job: 'civ', title: 'كراج القوارب',
            x: 1684.4138, y: 4041.6301, z: 35.3346, h: 44.5069, spawn: { x: 1692.8137, y: 4044.4387, z: 29.7621, h: 35.8697 }
        },
        {
            type: 'helicopter', job: 'civ', title: 'كراج المروحيات',
            x: -1016.8640, y: -3506.2617, z: 14.1434, h: 62.9980
        },
        {
            type: 'helicopter', job: 'civ', title: 'كراج المروحيات',
            x: -745.3396, y: -1468.6569, z: 4.9961, h: 320.9030
        },
        {
            type: 'helicopter', job: 'civ', title: 'كراج المروحيات',
            x: 1770.1626, y: 3239.7393, z: 42.1251, h: 99.9568
        },
        {
            type: 'aircraft', job: 'civ', title: 'كراج الطائرات',
            x: -1072.0404, y: -3499.1912, z: 14.1434, h: 333.2326
        },
        {
            type: 'aircraft', job: 'civ', title: 'كراج الطائرات',
            x: 1692.3444, y: 3247.3853, z: 40.8872, h: 105.2247
        },
        // --------------- الحجز
        {
            type: 'car', job: 'impounded', title: 'حجز المركبات',
            x: 409.6076, y: -1623.1630, z: 29.0064, h: 143.4868, spawn: { x: 395.9602, y: -1644.7235, z: 29.2919, h: 317.6961 }
        },
        {
            type: 'car', job: 'impounded', title: 'حجز المركبات',
            x: -310.4132, y: -1162.0299, z: 22.7759, h: 269.4771, spawn: { x: -321.7208, y: -1167.0175, z: 22.8807, h: 177.5955 }
        },
        {
            type: 'car', job: 'impounded', title: 'حجز المركبات',
            x: 43.3250, y: 6561.8682, z: 30.7557, h: 221.5644, spawn: { x: 45.5099, y: 6559.5151, z: 31.2557, h: 222.5447 }
        },
        {
            type: 'car', job: 'impounded', title: 'حجز المركبات',
            x: 1464.5469, y: 3580.8452, z: 34.8776, h: 21.2067, spawn: { x: 1422.4172, y: 3565.5642, z: 34.8634, h: 194.1816 }
        },
        {
            type: 'truck', job: 'impounded', title: 'حجز الشاحنات',
            x: 924.3192, y: -1533.4215, z: 30.4435, h: 87.9824, spawn: { x: 911.1558, y: -1533.8666, z: 30.7131, h: 89.7467 }
        },
        {
            type: 'truck', job: 'impounded', title: 'حجز الشاحنات',
            x: 924.5116, y: -1526.1089, z: 30.4435, h: 91.7076, spawn: { x: 913.0980, y: -1526.1339, z: 30.8263, h: 88.5451 }
        },
        {
            type: 'truck', job: 'impounded', title: 'حجز الشاحنات',
            x: 179.5061, y: 2758.7341, z: 43.1277, h: 100.2012, spawn: { x: 171.2066, y: 2757.5164, z: 43.4277, h: 95.0746 }
        },
        {
            type: 'truck', job: 'impounded', title: 'حجز الشاحنات',
            x: 123.8568, y: 6544.4814, z: 30.7557, h: 134.4375, spawn: { x: 115.3960, y: 6536.2329, z: 31.2557, h: 134.0547 }
        },
        // {
        //     type: 'truck', job: 'impounded', title: 'حجز الشاحنات',
        //     x: 58.3693, y: 6491.9414, z: 30.7976, h: 312.3922, spawn: { x: 65.4625, y: 6499.1519, z: 31.3730, h: 314.8477 }
        // },
        {
            type: 'boat', job: 'impounded', title: 'حجز القوارب',
            x: -787.3384, y: -1508.1246, z: 1.5955, h: 107.6088, spawn: { x: -797.3044, y: -1502.9792, z: 1.1755, h: 109.0463 }
        },
        {
            type: 'boat', job: 'impounded', title: 'حجز القوارب',
            x: 1659.5536, y: 4007.1123, z: 35.4362, h: 122.6670, spawn: { x: 1662.8286, y: 4022.8960, z: 29.5306, h: 124.1524 }
        },
        {
            type: 'helicopter', job: 'impounded', title: 'حجز المروحيات',
            x: -1032.3120, y: -3535.5906, z: 14.1434, h: 62.9774
        },
        {
            type: 'helicopter', job: 'impounded', title: 'حجز المروحيات',
            x: 1749.5874, y: 3236.6086, z: 41.9592, h: 111.3774
        },
        {
            type: 'aircraft', job: 'impounded', title: 'حجز الطائرات',
            x: -1057.9464, y: -3508.7742, z: 14.1434, h: 325.8712
        },
        // --------------- الشرطة
        {
            type: 'car', job: 'police', title: 'مركبات الشرطة',
            x: 569.8546, y: 4.9426, z: 70.1131, h: 71.0444, spawn: { x: 566.4333, y: 6.1947, z: 70.6131, h: 68.2411 }
        },
        {
            type: 'car', job: 'police', title: 'مركبات الشرطة',
            x: 621.4583, y: 26.2429, z: 87.7750, h: 356.9082, spawn: { x: 621.2076, y: 30.6339, z: 88.6908, h: 0.6502 }
        },
        {
            type: 'car', job: 'police', title: 'مركبات الشرطة',
            x: 2555.7007, y: -409.4802, z: 92.5934, h: 342.6952, spawn: { x: 2556.6736, y: -406.0794, z: 92.9934, h: 342.5316 }
        },
        {
            type: 'car', job: 'police', title: 'مركبات الشرطة',
            x: 2552.2241, y: -392.3399, z: 92.5933, h: 5.7206, spawn: { x: 2551.2622, y: -387.5774, z: 92.9933, h: 10.4207 }
        },
        {
            type: 'car', job: 'police', title: 'مركبات الشرطة',
            x: 822.7264, y: -1257.6954, z: 25.8485, h: 177.6775, spawn: { x: 822.7264, y: -1257.6954, z: 26.2485, h: 177.6775 }
        },
        {
            type: 'car', job: 'police', title: 'مركبات الشرطة',
            x: 1861.1688, y: 3700.2717, z: 33.3746, h: 123.8843, spawn: { x: 1858.2034, y: 3698.1492, z: 33.9746, h: 122.8261 }
        },
        {
            type: 'car', job: 'police', title: 'مركبات الشرطة',
            x: -480.2655, y: 6028.6406, z: 30.7404, h: 224.9919, spawn: { x: -477.9402, y: 6026.4795, z: 31.3404, h: 223.7077 }
        },
        {
            type: 'truck', job: 'police', title: 'شاحنات الشرطة',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'boat', job: 'police', title: 'قوارب الشرطة',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'helicopter', job: 'police', title: 'مروحيات الشرطة',
            x: -475.1967, y: 5988.6499, z: 30.8367, h: 315.9023
        },
        {
            type: 'aircraft', job: 'police', title: 'طائرات الشرطة',
            x: 0, y: 0, z: 0, h: 0
        },
        // --------------- أمن المنشآت
        {
            type: 'car', job: 'agent', title: 'مركبات أمن المنشآت',
            x: 870.5884, y: -2940.2563, z: 5.5016, h: 91.3861, spawn: { x: 865.9169, y: -2940.2480, z: 5.9016, h: 89.3566 }
        },
        {
            type: 'car', job: 'agent', title: 'مركبات أمن المنشآت',
            x: 261.5343, y: -334.9178, z: 44.3196, h: 245.4659, spawn: { x: 267.1297, y: -337.1687, z: 44.9196, h: 246.6086 }
        },
        {
            type: 'car', job: 'agent', title: 'مركبات أمن المنشآت',
            x: -400.7331, y: 6161.3291, z: 30.8781, h: 353.3416, spawn: { x: -399.9899, y: 6166.0664, z: 31.4937, h: 352.0876 }
        },
        {
            type: 'car', job: 'agent', title: 'مركبات أمن المنشآت',
            x: 768.2089, y: -2972.7290, z: 4.7286, h: 85.9066, spawn: { x: 763.7899, y: -2972.7000, z: 5.4286, h: 90.6364 }
        },
        {
            type: 'car', job: 'agent', title: 'مركبات أمن المنشآت',
            x: -2985.1128, y: 2690.5964, z: 8.6892, h: 68.2866, spawn: { x: -2989.0452, y: 2692.1475, z: 8.9892, h: 68.3858 }
        },
        {
            type: 'car', job: 'agent', title: 'مركبات أمن المنشآت',
            x: 1851.6163, y: 3693.8887, z: 33.4747, h: 279.0843, spawn: { x: 1856.2800, y: 3694.6367, z: 33.9747, h: 279.1237 }
        },
        {
            type: 'truck', job: 'agent', title: 'شاحنات أمن المنشآت',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'boat', job: 'agent', title: 'قوارب أمن المنشآت',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'helicopter', job: 'agent', title: 'مروحيات أمن المنشآت',
            x: 834.2668, y: -2913.8323, z: 7.7486, h: 86.7404
        },
        {
            type: 'aircraft', job: 'agent', title: 'طائرات أمن المنشآت',
            x: 0, y: 0, z: 0, h: 0
        },
        // --------------- الدفاع المدني
        {
            type: 'car', job: 'ambulance', title: 'مركبات الدفاع المدني',
            x: -291.6007, y: -637.7404, z: 32.1741, h: 357.4342, spawn: { x: -291.7202, y: -631.6907, z: 32.7741, h: 0.3833 }
        },
        {
            type: 'car', job: 'ambulance', title: 'مركبات الدفاع المدني',
            x: 296.4389, y: -1446.6632, z: 29.5669, h: 131.5095, spawn: { x: 299.4476, y: -1441.0869, z: 29.7930, h: 226.8353 }
        },
        {
            type: 'car', job: 'ambulance', title: 'مركبات الدفاع المدني',
            x: 1161.3284, y: -1519.1940, z: 34.8434, h: 343.1916, spawn: { x: 1159.6772, y: -1504.3715, z: 34.6926, h: 85.9431 }
        },
        {
            type: 'car', job: 'ambulance', title: 'مركبات الدفاع المدني',
            x: -254.4792, y: 6339.0942, z: 32.4261, h: 314.0304, spawn: { x: -260.1725, y: 6344.1494, z: 32.4262, h: 271.0308 }
        },
        {
            type: 'car', job: 'ambulance', title: 'مركبات الدفاع المدني',
            x: 1095.1005, y: 2730.8811, z: 38.2188, h: 89.2404, spawn: { x: 1091.3495, y: 2726.7349, z: 38.6723, h: 358.1986 }
        },
        {
            type: 'truck', job: 'ambulance', title: 'شاحنات الدفاع المدني',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'boat', job: 'ambulance', title: 'قوارب الدفاع المدني',
            x: 0, y: 0, z: 0, h: 0, spawn: { x: 0, y: 0, z: 0, h: 0 }
        },
        {
            type: 'helicopter', job: 'ambulance', title: 'مروحيات الدفاع المدني',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'aircraft', job: 'ambulance', title: 'طائرات الدفاع المدني',
            x: 0, y: 0, z: 0, h: 0
        },
        // --------------- الميكانيكي
        {
            type: 'car', job: 'mechanic', title: 'مركبات الميكانيكي',
            x: -365.8638, y: -109.1873, z: 38.2969, h: 161.1029, spawn: { x: -368.3556, y: -117.1661, z: 38.6965, h: 160.2025 }
        },
        {
            type: 'car', job: 'mechanic', title: 'مركبات الميكانيكي',
            x: 88.8359, y: 6544.9399, z: 30.7557, h: 315.4146, spawn: { x: 100.2849, y: 6539.9053, z: 31.2557, h: 39.8044 }
        },
        {
            type: 'car', job: 'mechanic', title: 'مركبات الميكانيكي',
            x: 188.0168, y: 3210.1416, z: 41.9235, h: 177.6768, spawn: { x: 194.3283, y: 3204.9858, z: 42.6187, h: 267.9814 }
        },
        {
            type: 'truck', job: 'mechanic', title: 'شاحنات الميكانيكي',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'boat', job: 'mechanic', title: 'قوارب الميكانيكي',
            x: 0, y: 0, z: 0, h: 0, spawn: { x: 0, y: 0, z: 0, h: 0 }
        },
        {
            type: 'helicopter', job: 'mechanic', title: 'مروحيات الميكانيكي',
            x: 0, y: 0, z: 0, h: 0
        },
        {
            type: 'aircraft', job: 'mechanic', title: 'طائرات الميكانيكي',
            x: 0, y: 0, z: 0, h: 0
        },
    ],
    isOpen: false,
    runClose: false,
    runCloseStorage: false,
    pedId: 0,
    pedCoords: [],
    vehId: 0,
    vehicles: [],
    ownHouses: [],
    ownHousesStorage: []
};
// ---
const ESX = exports.es_extended.getSharedObject();
const empty = null;
;
/* ``````````` ## Development By el8rbawY ## ```````````*/
const housesData = [
    { action: "points", type: "car", useVehicle: false, location: { marker: [-864.4229, 698.3693, 149.0443], spawner: [-864.4229, 698.3693, 149.0443], heading: 353.1427, private: "HillcrestAvenue2874" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBOldSpiceClassical" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [391.8027, 429.5692, 143.7715], spawner: [391.1044, 432.7659, 143.1347], heading: 259.9383, private: "NorthConkerAvenue2045" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-683.0345, 602.1692, 143.6278], spawner: [-685.4974, 604.2943, 143.6750], heading: 322.0516, private: "HillcrestAvenue2862" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBPowerBrokerPolished" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWOldSpiceWarm" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "PowerBrokerIce" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MazeBankWest" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1477.15, -517.17, 34.74], spawner: [-1483.16, -505.1, 32.81], heading: 299.89, private: "DellPerroHeightst4" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-887.5, -349.58, 34.53], spawner: [-886.03, -343.78, 34.53], heading: 206.79, private: "RichardMajesticApt2" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-14.1, -614.93, 35.86], spawner: [-7.35, -635.1, 35.72], heading: 66.632, private: "IntegrityWay28" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "OldSpiceClassical" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1290.95, 454.52, 97.66], spawner: [-1297.62, 459.28, 97.48], heading: 285.652, private: "MadWayneThunder" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LomBank" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBPowerBrokerConservative" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBExecutiveCool" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWOldSpiceClassical" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-14.1, -614.93, 35.86], spawner: [-7.35, -635.1, 35.72], heading: 66.632, private: "IntegrityWay30" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBExecutiveRich" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [240.23, 3102.84, 42.49], spawner: [233.58, 3094.29, 42.49], heading: 93.91, private: "MedEndApartment1" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Modern3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWOldSpiceVintage" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-24.6, 6605.99, 31.45], spawner: [-16, 6607.74, 31.18], heading: 35.31, private: "MedEndApartment9" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1477.15, -517.17, 34.74], spawner: [-1483.16, -505.1, 32.81], heading: 299.89, private: "DellPerroHeightst7" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-374.73, 6187.06, 31.54], spawner: [-377.97, 6183.73, 31.49], heading: 223.71, private: "MedEndApartment8" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1477.15, -517.17, 34.74], spawner: [-1483.16, -505.1, 32.81], heading: 299.89, private: "DellPerroHeights" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWExecutiveContrast" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "OldSpiceVintage" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Seductive2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [246.08, 3174.63, 42.72], spawner: [234.15, 3164.37, 42.54], heading: 102.03, private: "MedEndApartment2" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBPowerBrokerIce" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-752.75, 624.9, 142.2], spawner: [-749.32, 628.61, 142.48], heading: 197.14, private: "HillcrestAvenue2868" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-616.74, 56.38, 43.73], spawner: [-620.59, 60.1, 43.73], heading: 109.316, private: "TinselTowersApt12" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "PowerBrokerPolished" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Vibrant3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Vibrant2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Modern1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWExecutiveCool" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "PowerBrokerConservative" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBOldSpiceVintage" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Seductive1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Regal3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-365.18, 6323.95, 29.9], spawner: [-359.49, 6327.41, 29.83], heading: 218.58, private: "MedEndApartment10" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [20.3226, 6578.3599, 32.0854], spawner: [20.3226, 6578.3599, 32.0854], heading: 224.4919, private: "MedEndApartment7" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "ExecutiveContrast" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Aqua3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "MazeBankBuilding" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [1724.49, 4638.13, 43.31], spawner: [1723.98, 4630.19, 43.23], heading: 117.88, private: "MedEndApartment5" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [196.49, 3027.48, 43.89], spawner: [203.1, 3039.47, 43.08], heading: 271.3, private: "MedEndApartment4" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [984.92, 2668.95, 40.06], spawner: [993.96, 2672.68, 40.06], heading: 0.61, private: "MedEndApartment3" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [352.6343, 437.3198, 148.0291], spawner: [358.39, 437.06, 145.27], heading: 285.911, private: "NorthConkerAvenue2044" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [123.65, 565.75, 184.04], spawner: [130.11, 571.47, 183.42], heading: 270.71, private: "WhispymoundDrive" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Vibrant1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Sharp3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-178.65, 503.45, 136.85], spawner: [-189.98, 505.8, 134.48], heading: 282.62, private: "WildOatsDrive" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "OldSpiceWarm" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Monochrome1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [1670.76, 4740.99, 42.08], spawner: [1673.47, 4756.51, 41.91], heading: 12.82, private: "MedEndApartment6" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Aqua2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Modern2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBOldSpiceWarm" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Regal2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Regal1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Mody1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-14.1, -614.93, 35.86], spawner: [-7.35, -635.1, 35.72], heading: 66.632, private: "IntegrityWay" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Monochrome3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Monochrome2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Sharp2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Sharp1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Mody3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWPowerBrokerPolished" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Aqua1Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWPowerBrokerIce" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Seductive3Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "Mody2Apartment" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWPowerBrokerConvservative" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-795.96, 331.83, 85.5], spawner: [-800.49, 333.47, 85.5], heading: 180.494, private: "MiltonDrive" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "ExecutiveRich" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1376.93, -474.32, 31.5], heading: 97.95, private: "MBWExecutiveRich" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1551.88, -581.38, 25.71], heading: 331.176, private: "LBExecutiveContrast" } },
    { action: "points", type: "car", useVehicle: false, location: { marker: [-60.38, -790.31, 44.23], spawner: [-44.03, -787.36, 44.19], heading: 254.322, private: "ExecutiveCool" } },
    // --------------------------------------------
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-374.73, 6187.06, 31.54], spawner: [-383.31, 6188.85, 31.49], private: "MedEndApartment8" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Mody1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LomBank" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Seductive2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-24.6, 6605.99, 31.45], spawner: [-9.36, 6598.86, 31.47], private: "MedEndApartment9" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBOldSpiceWarm" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBPowerBrokerConservative" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [20.3226, 6578.3599, 32.0854], spawner: [10.45, 6588.04, 32.47], private: "MedEndApartment7" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWExecutiveContrast" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBExecutiveCool" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [240.23, 3102.84, 42.49], spawner: [237.52, 3112.63, 42.39], private: "MedEndApartment1" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBExecutiveRich" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Sharp1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-683.0345, 602.1692, 143.6278], spawner: [-685.26, 601.08, 143.36], private: "HillcrestAvenue2862" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBPowerBrokerIce" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWExecutiveCool" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Aqua2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWExecutiveRich" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [1670.76, 4740.99, 42.08], spawner: [1668.46, 4750.83, 41.88], private: "MedEndApartment6" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-14.1, -614.93, 35.86], spawner: [-37.57, -620.39, 35.07], private: "IntegrityWay" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Modern1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [391.8027, 429.5692, 143.7715], spawner: [389.72, 429.95, 142.81], private: "NorthConkerAvenue2045" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWOldSpiceVintage" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Aqua3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "ExecutiveCool" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWPowerBrokerIce" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWOldSpiceWarm" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "MazeBankBuilding" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBOldSpiceVintage" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-365.18, 6323.95, 29.9], spawner: [-353.47, 6334.57, 29.83], private: "MedEndApartment10" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-887.5, -349.58, 34.53], spawner: [-894.32, -349.33, 34.53], private: "RichardMajesticApt2" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [196.49, 3027.48, 43.89], spawner: [192.24, 3037.95, 43.89], private: "MedEndApartment4" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [984.92, 2668.95, 40.06], spawner: [994.04, 2662.1, 40.13], private: "MedEndApartment3" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "OldSpiceWarm" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [246.08, 3174.63, 42.72], spawner: [240.72, 3165.53, 42.65], private: "MedEndApartment2" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBOldSpiceClassical" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-616.74, 56.38, 43.73], spawner: [-621.13, 52.69, 43.73], private: "TinselTowersApt12" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1290.95, 454.52, 97.66], spawner: [-1298.09, 468.95, 97], private: "MadWayneThunder" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-864.4229, 698.3693, 149.0443], spawner: [-855.66, 698.77, 148.81], private: "HillcrestAvenue2874" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWOldSpiceClassical" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-752.75, 624.9, 142.2], spawner: [-754.28, 631.58, 142.2], private: "HillcrestAvenue2868" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "ExecutiveContrast" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [352.6343, 437.3198, 148.0291], spawner: [355.4699, 438.7993, 146.9925], private: "NorthConkerAvenue2044" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Monochrome1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Seductive1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [1724.49, 4638.13, 43.31], spawner: [1733.66, 4635.08, 43.24], private: "MedEndApartment5" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "ExecutiveRich" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "OldSpiceVintage" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Aqua1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Regal3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Regal2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Vibrant3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-14.1, -614.93, 35.86], spawner: [-37.57, -620.39, 35.07], private: "IntegrityWay28" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Seductive3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Modern2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Mody2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-14.1, -614.93, 35.86], spawner: [-37.57, -620.39, 35.07], private: "IntegrityWay30" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-178.65, 503.45, 136.85], spawner: [-189.28, 500.56, 133.93], private: "WildOatsDrive" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Monochrome3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Monochrome2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "MiltonDrive" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MazeBankWest" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [123.65, 565.75, 184.04], spawner: [131.97, 566.77, 182.95], private: "WhispymoundDrive" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Sharp3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Sharp2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Vibrant1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBPowerBrokerPolished" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Vibrant2Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Modern3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "PowerBrokerIce" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Mody3Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1545.17, -566.24, 25.85], spawner: [-1538.56, -576.05, 25.71], private: "LBExecutiveContrast" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1477.15, -517.17, 34.74], spawner: [-1452.61, -508.78, 31.58], private: "DellPerroHeightst4" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1477.15, -517.17, 34.74], spawner: [-1452.61, -508.78, 31.58], private: "DellPerroHeightst7" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1477.15, -517.17, 34.74], spawner: [-1452.61, -508.78, 31.58], private: "DellPerroHeights" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "OldSpiceClassical" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-795.96, 331.83, 85.5], spawner: [-791.75, 333.47, 85.5], private: "Regal1Apartment" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "PowerBrokerConservative" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWPowerBrokerPolished" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-1368.14, -468.01, 31.6], spawner: [-1362.07, -471.98, 31.5], private: "MBWPowerBrokerConvservative" } },
    { action: "delete", type: "car", useVehicle: true, location: { marker: [-60.38, -790.31, 44.23], spawner: [-58.88, -778.63, 44.18], private: "PowerBrokerPolished" } },
];
/* ``````````` ## Development By el8rbawY ## ```````````*/
for (const item of state.blips) {
    createBlip(item);
}
// ---
let current = null, timeoutId = 0;
// ---
setTick(() => {
    state.pedId = PlayerPedId();
    state.pedCoords = GetEntityCoords(state.pedId, true);
    state.vehId = GetVehiclePedIsIn(state.pedId, false);
    if (!state.vehId) {
        for (const item of [...state.garages, ...state.ownHouses]) {
            const distance = GetDistanceBetweenCoords(state.pedCoords[0], state.pedCoords[1], state.pedCoords[2], item.x, item.y, item.z, true);
            if (distance < 40) {
                let color;
                if (item.job === 'impounded') {
                    color = [227, 161, 28, 100];
                } else {
                    color = item.blipId ? [51, 153, 102, 150] : [0, 128, 255, 100];
                }

                if (item.type === 'car')
                    DrawMarker(36, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, color[0], color[1], color[2], color[3], false, true, 2, false, empty, empty, false);
                else if (item.type === 'truck')
                    DrawMarker(39, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, color[0], color[1], color[2], color[3], false, true, 2, false, empty, empty, false);
                else if (item.type === 'boat')
                    DrawMarker(35, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, color[0], color[1], color[2], color[3], false, true, 2, false, empty, empty, false);
                else if (item.type === 'helicopter')
                    DrawMarker(34, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, color[0], color[1], color[2], color[3], false, true, 2, false, empty, empty, false);
                else if (item.type === 'aircarft')
                    DrawMarker(33, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, color[0], color[1], color[2], color[3], false, true, 2, false, empty, empty, false);

                if (distance < 1.5) {
                    current = item;
                    break;
                } else {
                    current = null;
                }
            } else {
                current = null;
            }
        }
    }
    else
        current = null;
        if (current) {
            if (IsControlJustPressed(0, 38) && !timeoutId) {
                ESX.TriggerServerCallback('oscar:server:getBill', (data) => {
                    if (data >= 3) { // bill
                        exports.OscarCounty_Notifications.showAttention('error', 'يجب دفع مخالفاتك أولا لفتح قائمة الكراج');
                    }
                    else if (data < 3) { // bill                
            const jobs = ['police', 'agent', 'ambulance', 'mechanic'];
            if (jobs.includes(current.job) && current.job !== exports.es_extended.getSharedObject().PlayerData.job?.name) {
                return exports.OscarCounty_Notifications.showAttention('error', 'وظيفتك الحالية لا تتوافق مع نوع الكراج');
            }
            emitNet('HyperScript_Garage:handleGeneral-server', 'getVehicles', { job: current.job, type: current.type });
            SendNUIMessage(JSON.stringify({ type: 'entranceOpen', value: false }));
            // استخدام progress bar آمن
            try {
                exports.pogressBar.drawBar(3000, 'جاري التنفيذ');
            } catch (e) {
                // بديل إذا لم يكن progress bar متاح
                exports.es_extended.getSharedObject().ShowNotification('جاري التنفيذ...');
            }
            state.isOpen = true;
            timeoutId = setTimeout(() => {
                if (state.vehicles.length) {
                    SendNUIMessage(JSON.stringify({ 
                        type: 'setData', info: { 
                           type: current?.job, 
                           price: exports.OscarCounty_Web.method('isHaveSponsor') ? 0 : state.impoundedPrice, 
                           title: current?.title, 
                           items: state.vehicles 
                        }
                     }));
                    SetNuiFocus(true, true);
                }
                else {
                    exports.OscarCounty_Notifications.showAttention('error', 'لا يوجد أي مركبات لديك لهذا النوع من الكراج!');
                }
                timeoutId = 0;
            }, 3000);
        }
    }, 'GetBils');
}
else if (!state.isOpen && !state.runClose) {
    SendNUIMessage(JSON.stringify({ type: 'entranceOpen', value: true }));
}
state.runClose = true;
}
else if (state.runClose) {
closeUI(true);
}
});
// ---
RegisterNuiCallbackType('NUI:update');
on('__cfx_nui:NUI:update', (data, cb) => {
    switch (data.type) {
        case 'spawn':
            if (!data.isImpounded && data.impounded) {
                exports.OscarCounty_Notifications.showAttention('error', 'يجب فك حجز المركبة اولا!');
                return cb('OK!');
            }
            else if (data.isImpounded) {
                const isHaveSponsor = exports.OscarCounty_Web.method('isHaveSponsor');

                if (!isHaveSponsor) {
                    const accounts = exports.es_extended.getSharedObject().PlayerData.accounts;
                    const cash = accounts.find((item) => item.name === 'money').money;
                    const bank = accounts.find((item) => item.name === 'bank').money;
        
                    if (cash < state.impoundedPrice && bank < state.impoundedPrice) {
                        exports.OscarCounty_Notifications.showAttention('success', 'للأسف لا تملك المال الكافي للدفع!');
                        return cb('OK!');
                    }
                }
                emitNet('HyperScript_Garage:handleGeneral-server', 'unImpounded', { plate: data.plate, isHaveSponsor });
                spawnVehicle(data.plate);
            }
            else if (!data.outside) {
                spawnVehicle(data.plate);
            }
            closeUI(true);
            break;
        default: closeUI(false);
    }
    cb('OK!');
});
/* ``````````` ## Development By el8rbawY ## ```````````*/
onNet('HyperScript_Garage:handleGeneral-client', (type, data) => {
    if (type === 'setVehicles') {
        state.vehicles = data;
    }
});
// ---
onNet('esx_advancedgarage:getPropertiesC', () => {
    ESX.TriggerServerCallback('HyperScript_Garage:callback-server', (data) => {
        for (const item of state.ownHouses)
            RemoveBlip(item.blipId);
        state.ownHouses = [];
        state.ownHousesStorage = [];
        for (const item of data) {
            const point = housesData.find(house => house.action === 'points' && house.location.private === item.name);
            const storage = housesData.find(house => house.action === 'delete' && house.location.private === item.name);
            if (point) {
                const [x, y, z] = point.location.marker;
                state.ownHouses.push({
                    title: 'كراج العقار', type: 'car', job: 'civ', blipId: createBlip({ id: 50, title: 'ﺭﺎﻘﻌﻟﺍ ﺝﺍﺮﻛ', color: 2, coords: { x, y, z } }),
                    x, y, z, spawn: { x: point.location.spawner[0], y: point.location.spawner[1], z: point.location.spawner[2], h: point.location.heading || 0 }
                });
            }
            if (storage) {
                const [x, y, z] = storage.location.marker;
                state.ownHousesStorage.push({ type: 'car', x, y, z });
            }
        }
    }, 'getOwnedHouses');
});
// ---
function spawnVehicle(plate) {
    const find = state.vehicles.find(veh => veh.plate === plate);
    if (find) {
        const properties = JSON.parse(find.vehicle);
        const coords = current?.spawn || { x: current?.x, y: current?.y, z: current?.z, h: current?.h };
        ESX.Game.SpawnVehicle(properties.model, coords, coords.h, (vehId) => {
            ESX.Game.SetVehicleProperties(vehId, properties);
            SetVehRadioStation(vehId, 'OFF');
            SetVehicleUndriveable(vehId, false);
            SetVehicleEngineOn(vehId, false, true, false);
            TaskWarpPedIntoVehicle(PlayerPedId(), vehId, -1);
            emitNet('HyperScript_Garage:handleGeneral-server', 'vehStatus', { plate, value: true });
        }, true);
    }
}
// ---
function createBlip(item) {
    const blip = AddBlipForCoord(item.coords.x, item.coords.y, item.coords.z);
    SetBlipSprite(blip, item.id);
    SetBlipColour(blip, item.color);
    SetBlipAsShortRange(blip, true);
    BeginTextCommandSetBlipName("STRING");
    AddTextComponentString(`<font face="A9eelsh">${item.title}</font>`);
    EndTextCommandSetBlipName(blip);
    return blip;
}
// ---
function closeUI(withNUI) {
    clearTimeout(timeoutId);
    timeoutId = 0;
    if (withNUI)
        SendNUIMessage(JSON.stringify({ type: 'toggleUI', value: false }));
    SetNuiFocus(false, false);
    state.isOpen = false;
    state.runClose = false;
    state.runCloseStorage = false;
    state.vehicles = [];
}
/* ``````````` ## Development By el8rbawY ## ```````````*/
setTick(() => {
    let currentType = '';
    let currentJob  = '';

    if (state.vehId) {
        for (const item of [...state.storages, ...state.ownHousesStorage]) {
            const distance = GetDistanceBetweenCoords(
                state.pedCoords[0], state.pedCoords[1], state.pedCoords[2],
                item.x, item.y, item.z, true
            );

            if (distance < 40) {
                if (item.type === 'car')
                    DrawMarker(36, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, 200, 0, 0, 125, false, true, 2, false, empty, empty, false);
                else if (item.type === 'truck')
                    DrawMarker(39, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, 200, 0, 0, 125, false, true, 2, false, empty, empty, false);
                else if (item.type === 'boat')
                    DrawMarker(35, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, 200, 0, 0, 125, false, true, 2, false, empty, empty, false);
                else if (item.type === 'helicopter')
                    DrawMarker(34, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, 200, 0, 0, 125, false, true, 2, false, empty, empty, false);
                else if (item.type === 'aircarft')
                    DrawMarker(33, item.x, item.y, item.z + 0.3, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.5, 1.5, 1.5, 200, 0, 0, 125, false, true, 2, false, empty, empty, false);

                if (distance < 5) {
                    currentType = item.type;
                    currentJob  = item.job || 'civ';
                    break;
                }
            }
        }
    }

    if (currentType) {
        if (IsControlJustPressed(0, 38)) {
            const plate = GetVehicleNumberPlateText(state.vehId)?.trim();
            if (GetPedInVehicleSeat(state.vehId, -1) !== state.pedId) {
                exports.OscarCounty_Notifications.showAttention('error', 'يجب أن تكون في مقعد السائق لتخزين المركبة!');
                return;
            }
    
            ESX.TriggerServerCallback('HyperScript_Garage:callback-server', (data) => {
                // التحقق من وجود البيانات
                if (!data) {
                    exports.OscarCounty_Notifications.showAttention('error', 'خطأ في النظام، حاول مرة أخرى!');
                    return;
                }

                console.log('Callback data:', data);

                if (data.isOwner && currentType === data.vehType && currentJob === data.vehJob) {
                    const trailer = IsVehicleAttachedToTrailer(state.vehId);
                    if (trailer) {
                        const [retval, trailerEntity] = GetVehicleTrailerVehicle(state.vehId);
                        let NetId = NetworkGetNetworkIdFromEntity(trailerEntity);
                        emitNet('HyperScript_Garage:handleGeneral-server', 'vehStatus', { plate, value: false }, NetId);
                        const Tplate = GetVehicleNumberPlateText(trailerEntity)?.trim();

                        // محاولة الحصول على المركبات المرفقة بالمقطورة (اختياري)
                        try {
                            let attachedVehicles = exports.ab_Logs.GetAttachedVehicles(trailerEntity);
                            if (attachedVehicles && attachedVehicles.length > 0) {
                                for (let i = 0; i < attachedVehicles.length; i++) {
                                    let vehicle = attachedVehicles[i];
                                    try {
                                        exports.AdvancedParking.DeleteVehicle(vehicle, false);
                                    } catch (e) {
                                        // حذف عادي إذا لم يكن AdvancedParking متاح
                                        DeleteEntity(vehicle);
                                    }
                                }
                            }
                        } catch (e) {
                            // تجاهل إذا لم يكن ab_Logs متاح
                            console.log('ab_Logs not available for trailer, skipping attached vehicles check');
                        }

                        emitNet('HyperScript_Garage:handleGeneral-server', 'vehStatusTrailer', { Tplate, value: false }, Tplate);

                        // حذف المقطورة
                        try {
                            exports.AdvancedParking.DeleteVehicle(trailerEntity, false);
                        } catch (e) {
                            // حذف عادي إذا لم يكن AdvancedParking متاح
                            DeleteEntity(trailerEntity);
                        }
                    }
                    else {
                        let NetId = NetworkGetNetworkIdFromEntity(state.vehId);

                        // محاولة الحصول على المركبات المرفقة (اختياري)
                        try {
                            let attachedVehicles = exports.ab_Logs.GetAttachedVehicles(state.vehId);
                            if (attachedVehicles && attachedVehicles.length > 0) {
                                for (let i = 0; i < attachedVehicles.length; i++) {
                                    let vehicle = attachedVehicles[i];
                                    try {
                                        exports.AdvancedParking.DeleteVehicle(vehicle, false);
                                    } catch (e) {
                                        // حذف عادي إذا لم يكن AdvancedParking متاح
                                        DeleteEntity(vehicle);
                                    }
                                }
                            }
                        } catch (e) {
                            // تجاهل إذا لم يكن ab_Logs متاح
                            console.log('ab_Logs not available, skipping attached vehicles check');
                        }

                        emitNet('HyperScript_Garage:handleGeneral-server', 'vehStatus', { plate, value: false }, plate);
                    }

                    // حذف المركبة الرئيسية
                    try {
                        exports.AdvancedParking.DeleteVehicle(state.vehId, false);
                    } catch (e) {
                        // حذف عادي إذا لم يكن AdvancedParking متاح
                        DeleteEntity(state.vehId);
                    }
                    exports.OscarCounty_Notifications.showAttention('success', 'لقد قمت بتخزين المركبة بالكراج');
                }
                else if (!data.isOwner) {
                    exports.OscarCounty_Notifications.showAttention('error', 'المركبة ليست ملكك لحجزها!');
                }
                else if (currentType !== data.vehType) {
                    exports.OscarCounty_Notifications.showAttention('error', 'لا يمكن تحزين هذا النوع من المركبات هنا!');
                }
                else {
                    exports.OscarCounty_Notifications.showAttention('error', 'هذا الكراج مخصص لوظيفة مختلفة!');
                }
            }, 'isCanStorage', { plate });
        }
        else if (!state.runCloseStorage) {
            SendNUIMessage(JSON.stringify({ type: 'entranceOpen', value: true }));
        }
        state.runCloseStorage = true;
    }
    else if (state.runCloseStorage) {
        closeUI(true);
    }
    });
    