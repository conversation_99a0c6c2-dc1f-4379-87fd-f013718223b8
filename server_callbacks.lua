-- ملف callbacks منفصل لضمان التحميل الصحيح
-- Separate callbacks file to ensure proper loading

-- التأكد من تحميل ESX أولاً
local ESX = exports.es_extended:getSharedObject()

-- انتظار تحميل ESX بالكامل
Citizen.CreateThread(function()
    while not ESX do
        Citizen.Wait(100)
        ESX = exports.es_extended:getSharedObject()
    end
    
    print('^2[GARAGE CALLBACKS] ^7ESX loaded, registering callbacks...')
    
    -- تسجيل callback المطلوب
    ESX.RegisterServerCallback('oscar:server:getBill', function(source, cb, action)
        local xPlayer = ESX.GetPlayerFromId(source)
        
        if not xPlayer then
            print('^1[GARAGE CALLBACKS] ^7Player not found for source: ' .. tostring(source))
            cb(0)
            return
        end
        
        -- إرجاع 0 مخالفات دائماً
        cb(0)
        print('^2[<PERSON>RAGE CALLBACKS] ^7Bill check for player ' .. xPlayer.identifier .. ': 0 bills')
    end)
    
    -- callback إضافي للتوافق
    ESX.RegisterServerCallback('GetBils', function(source, cb)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            cb(0)
            print('^2[GARAGE CALLBACKS] ^7GetBils callback for player ' .. xPlayer.identifier .. ': 0 bills')
        else
            cb(0)
        end
    end)
    
    -- callback للتحقق من حالة اللاعب
    ESX.RegisterServerCallback('garage:checkPlayerStatus', function(source, cb)
        local xPlayer = ESX.GetPlayerFromId(source)
        
        if not xPlayer then
            cb({ bills = 0, canUseGarage = false })
            return
        end
        
        cb({
            bills = 0,
            canUseGarage = true,
            playerName = xPlayer.getName(),
            job = xPlayer.job.name,
            identifier = xPlayer.identifier
        })
    end)
    
    print('^2[GARAGE CALLBACKS] ^7All callbacks registered successfully!')
    
    -- التحقق من نجاح التسجيل
    Citizen.Wait(1000)
    print('^2[GARAGE CALLBACKS] ^7✓ oscar:server:getBill callback should be working now!')
end)

-- معالج لإعادة التسجيل عند الحاجة
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('^2[GARAGE CALLBACKS] ^7Resource started, callbacks should be ready!')
    end
end)

-- أمر للتحقق من حالة callbacks
RegisterCommand('check_callbacks', function(source)
    if source == 0 then -- من وحدة تحكم السيرفر
        print('^3[GARAGE CALLBACKS] ^7Checking callback status...')
        print('^2[CHECK] ^7Callbacks have been registered multiple times')
        print('^2[CHECK] ^7oscar:server:getBill should be working')
        print('^3[CHECK] ^7Use /test_hotfix in game to verify')
    end
end, true)

print('^2[GARAGE CALLBACKS] ^7Callbacks file loaded')
