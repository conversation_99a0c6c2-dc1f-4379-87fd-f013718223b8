-- ملف callbacks منفصل لضمان التحميل الصحيح
-- Separate callbacks file to ensure proper loading

-- التأكد من تحميل ESX أولاً
local ESX = exports.es_extended:getSharedObject()

-- انتظار تحميل ESX بالكامل
Citizen.CreateThread(function()
    while not ESX do
        Citizen.Wait(100)
        ESX = exports.es_extended:getSharedObject()
    end
    
    print('^2[GARAGE CALLBACKS] ^7ESX loaded, registering callbacks...')
    
    -- تسجيل callback المطلوب
    ESX.RegisterServerCallback('oscar:server:getBill', function(source, cb, action)
        local xPlayer = ESX.GetPlayerFromId(source)
        
        if not xPlayer then
            print('^1[GARAGE CALLBACKS] ^7Player not found for source: ' .. tostring(source))
            cb(0)
            return
        end
        
        -- إرجاع 0 مخالفات دائماً
        cb(0)
        print('^2[<PERSON>RAGE CALLBACKS] ^7Bill check for player ' .. xPlayer.identifier .. ': 0 bills')
    end)
    
    -- callback إضافي للتوافق
    ESX.RegisterServerCallback('GetBils', function(source, cb)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            cb(0)
            print('^2[GARAGE CALLBACKS] ^7GetBils callback for player ' .. xPlayer.identifier .. ': 0 bills')
        else
            cb(0)
        end
    end)
    
    -- callback للتحقق من حالة اللاعب
    ESX.RegisterServerCallback('garage:checkPlayerStatus', function(source, cb)
        local xPlayer = ESX.GetPlayerFromId(source)
        
        if not xPlayer then
            cb({ bills = 0, canUseGarage = false })
            return
        end
        
        cb({
            bills = 0,
            canUseGarage = true,
            playerName = xPlayer.getName(),
            job = xPlayer.job.name,
            identifier = xPlayer.identifier
        })
    end)
    
    print('^2[GARAGE CALLBACKS] ^7All callbacks registered successfully!')
    
    -- التحقق من نجاح التسجيل
    Citizen.Wait(1000)
    if ESX.ServerCallbacks and ESX.ServerCallbacks['oscar:server:getBill'] then
        print('^2[GARAGE CALLBACKS] ^7✓ oscar:server:getBill callback confirmed!')
    else
        print('^1[GARAGE CALLBACKS] ^7✗ oscar:server:getBill callback registration failed!')
    end
end)

-- معالج لإعادة التسجيل عند الحاجة
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Citizen.Wait(2000)
        
        -- التحقق مرة أخرى
        if not ESX.ServerCallbacks or not ESX.ServerCallbacks['oscar:server:getBill'] then
            print('^3[GARAGE CALLBACKS] ^7Re-registering missing callback...')
            
            ESX.RegisterServerCallback('oscar:server:getBill', function(source, cb, action)
                local xPlayer = ESX.GetPlayerFromId(source)
                if xPlayer then
                    cb(0)
                else
                    cb(0)
                end
            end)
        end
    end
end)

-- أمر للتحقق من حالة callbacks
RegisterCommand('check_callbacks', function(source)
    if source == 0 then -- من وحدة تحكم السيرفر
        print('^3[GARAGE CALLBACKS] ^7Checking callback status...')
        
        if ESX and ESX.ServerCallbacks then
            if ESX.ServerCallbacks['oscar:server:getBill'] then
                print('^2[CHECK] ^7✓ oscar:server:getBill: EXISTS')
            else
                print('^1[CHECK] ^7✗ oscar:server:getBill: MISSING')
            end
            
            if ESX.ServerCallbacks['GetBils'] then
                print('^2[CHECK] ^7✓ GetBils: EXISTS')
            else
                print('^1[CHECK] ^7✗ GetBils: MISSING')
            end
            
            print('^3[CHECK] ^7Total callbacks: ' .. (ESX.ServerCallbacks and #ESX.ServerCallbacks or 0))
        else
            print('^1[CHECK] ^7ESX or ServerCallbacks not available')
        end
    end
end, true)

print('^2[GARAGE CALLBACKS] ^7Callbacks file loaded')
