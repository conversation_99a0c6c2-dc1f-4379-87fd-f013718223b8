-- MySQL آمن ومبسط
-- Safe and simple MySQL

if IsDuplicityVersion() then
    -- Server-side safe MySQL
    
    local MySQL = nil
    local mysqlReady = false
    
    -- محاولة تحميل MySQL
    Citizen.CreateThread(function()
        Citizen.Wait(1000) -- انتظار تحميل الموارد
        
        -- محاولة mysql-async
        if GetResourceState('mysql-async') == 'started' then
            local success, result = pcall(function()
                return exports['mysql-async']
            end)
            
            if success and result then
                MySQL = result
                mysqlReady = true
                print('^2[MYSQL SAFE] ^7mysql-async loaded successfully')
            else
                print('^1[MYSQL SAFE] ^7Failed to load mysql-async: ' .. tostring(result))
            end
        end
        
        -- محاولة oxmysql إذا فشل mysql-async
        if not mysqlReady and GetResourceState('oxmysql') == 'started' then
            local success, result = pcall(function()
                return exports.oxmysql
            end)
            
            if success and result then
                MySQL = result
                mysqlReady = true
                print('^2[MYSQL SAFE] ^7oxmysql loaded successfully')
            else
                print('^1[MYSQL SAFE] ^7Failed to load oxmysql: ' .. tostring(result))
            end
        end
        
        if not mysqlReady then
            print('^1[MYSQL SAFE] ^7No MySQL resource available')
        end
    end)
    
    -- دالة آمنة للتنفيذ
    local function safeExecute(query, params, callback)
        if not mysqlReady or not MySQL then
            print('^1[MYSQL SAFE] ^7MySQL not ready for execute')
            if callback then callback(0) end
            return
        end
        
        local success, err = pcall(function()
            MySQL.mysql_execute(query, params, callback or function() end)
        end)
        
        if not success then
            -- محاولة طريقة أخرى
            success, err = pcall(function()
                MySQL.execute(query, params, callback or function() end)
            end)
        end
        
        if not success then
            print('^1[MYSQL SAFE] ^7Execute failed: ' .. tostring(err))
            if callback then callback(0) end
        end
    end
    
    -- دالة آمنة للجلب
    local function safeFetchAll(query, params, callback)
        if not mysqlReady or not MySQL then
            print('^1[MYSQL SAFE] ^7MySQL not ready for fetchAll')
            if callback then callback({}) end
            return
        end
        
        local success, err = pcall(function()
            MySQL.mysql_fetch_all(query, params, callback or function() end)
        end)
        
        if not success then
            -- محاولة طريقة أخرى
            success, err = pcall(function()
                MySQL.execute(query, params, callback or function() end)
            end)
        end
        
        if not success then
            print('^1[MYSQL SAFE] ^7FetchAll failed: ' .. tostring(err))
            if callback then callback({}) end
        end
    end
    
    -- تصدير الدوال
    exports('safeExecute', safeExecute)
    exports('safeFetchAll', safeFetchAll)
    exports('isMySQLReady', function() return mysqlReady end)
    
    -- أمر للاختبار
    RegisterCommand('test_mysql_safe', function(source)
        if source == 0 then
            print('^3[MYSQL SAFE] ^7Testing safe MySQL...')
            print('^3[MYSQL SAFE] ^7Ready: ' .. tostring(mysqlReady))
            
            if mysqlReady then
                safeFetchAll('SELECT 1 as test', {}, function(result)
                    if result and (result[1] or result.test) then
                        print('^2[MYSQL SAFE] ^7✓ Test successful')
                    else
                        print('^1[MYSQL SAFE] ^7✗ Test failed')
                    end
                end)
            end
        end
    end, true)
    
    print('^2[MYSQL SAFE] ^7Safe MySQL wrapper loaded')
end
