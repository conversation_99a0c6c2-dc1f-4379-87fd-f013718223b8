# دليل حل المشاكل - OscarCounty Garage System

## المشاكل الشائعة وحلولها

### 1. خطأ Callback المفقود

**الخطأ:**
```
Server Callback with requestId oscar:server:getBill Was Called but does not exist
```

**الحل:**
✅ تم إصلاح هذا الخطأ في الملفات المحدثة. تأكد من:
- وجود ملف `server.js` الجديد
- وجود ملف `compatibility.lua`
- إعادة تشغيل السيرفر

**إصلاح يدوي:**
```lua
-- أضف هذا في server.lua أو ملف منفصل
ESX.RegisterServerCallback('oscar:server:getBill', function(source, cb, action)
    cb(0) -- إرجاع 0 مخالفات افتراضياً
end)
```

### 2. مشكلة قاعدة البيانات

**الخطأ:**
```
Table 'database.owned_vehicles' doesn't exist
```

**الحل:**
```sql
-- تشغيل هذا في قاعدة البيانات
CREATE TABLE IF NOT EXISTS `owned_vehicles` (
  `owner` varchar(60) NOT NULL,
  `plate` varchar(12) NOT NULL,
  `vehicle` longtext,
  `stored` tinyint(1) NOT NULL DEFAULT 1,
  `impounded` tinyint(1) NOT NULL DEFAULT 0,
  `garage_type` varchar(50) DEFAULT 'car',
  PRIMARY KEY (`plate`)
);
```

### 3. مشكلة الإشعارات

**الخطأ:**
```
exports.OscarCounty_Notifications is not a function
```

**الحل:**
```lua
-- استخدام إشعارات ESX كبديل
ESX.ShowNotification('رسالتك هنا')

-- أو تثبيت نظام الإشعارات المطلوب
```

### 4. مشكلة شريط التقدم

**الخطأ:**
```
exports.pogressBar is not a function
```

**الحل:**
```lua
-- حذف أو تعليق هذا السطر في client.js
-- exports.pogressBar.drawBar(3000, 'جاري التنفيذ');

-- أو استبداله بـ
Wait(3000)
```

### 5. مشكلة عدم ظهور البليبس

**الأعراض:**
- لا تظهر علامات الكراجات على الخريطة

**الحل:**
```javascript
// في config.js
General: {
    enableBlips: true
}

// أو في client.js تأكد من تشغيل هذا الكود
for (const item of state.blips) {
    createBlip(item);
}
```

### 6. مشكلة عدم فتح الواجهة

**الأعراض:**
- الضغط على E لا يفتح الواجهة

**الحل:**
1. تحقق من وجود مجلد `ui_page/build`
2. تحقق من ملف `fxmanifest.lua`:
```lua
ui_page 'ui_page/build/index.html'
files { 
   'ui_page/build/*',
   'ui_page/build/static/css/*.css',
   'ui_page/build/static/js/*.js'
}
```

### 7. مشكلة عدم تخزين المركبات

**الأعراض:**
- المركبة لا تختفي عند التخزين
- رسالة خطأ عند محاولة التخزين

**الحل:**
```sql
-- تحقق من وجود الأعمدة المطلوبة
ALTER TABLE `owned_vehicles` 
ADD COLUMN IF NOT EXISTS `stored` tinyint(1) NOT NULL DEFAULT 1;
```

### 8. مشكلة الأذونات

**الخطأ:**
```
Access denied for user
```

**الحل:**
```sql
-- إعطاء صلاحيات للمستخدم
GRANT ALL PRIVILEGES ON database_name.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### 9. مشكلة عدم التوافق مع ESX

**الأعراض:**
- خطأ في تحميل ESX
- عدم عمل الأحداث

**الحل:**
```lua
-- تأكد من هذا الكود في بداية الملفات
local ESX = exports.es_extended:getSharedObject()

-- أو للإصدارات القديمة
ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
```

### 10. مشكلة الأداء

**الأعراض:**
- بطء في السيرفر
- استهلاك عالي للذاكرة

**الحل:**
```lua
-- تقليل تكرار التحديث في client.js
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500) -- زيادة الوقت من 0 إلى 500
        -- باقي الكود
    end
end)
```

## أوامر التصحيح

### في F8 (العميل):
```
/garage_debug          # فحص شامل للنظام
/test_garage          # اختبار أساسي
/test_config          # اختبار الإعدادات
/garage_help          # عرض المساعدة
```

### في وحدة تحكم السيرفر:
```
refresh               # إعادة تحميل السكربت
restart resource_name # إعادة تشغيل السكربت
```

## فحص السجلات

### سجلات العميل (F8):
```
resmon                # مراقبة الموارد
con_miniconChannels   # عرض قنوات التصحيح
```

### سجلات السيرفر:
```bash
tail -f server.log    # متابعة السجل المباشر
grep "GARAGE" server.log  # البحث عن رسائل الكراج
```

## إعدادات الطوارئ

### تعطيل الميزات المسببة للمشاكل:
```javascript
// في config.js
General: {
    enableBlips: false,        // تعطيل البليبس
    enableBillCheck: false,    // تعطيل فحص المخالفات
    enableJobRestrictions: false // تعطيل قيود الوظائف
}
```

### وضع الأمان:
```lua
-- في server.js - إضافة هذا في بداية كل callback
if not source or not ESX.GetPlayerFromId(source) then
    return
end
```

## الحصول على المساعدة

إذا لم تحل هذه الحلول مشكلتك:

1. **تحقق من الإصدار:** تأكد من استخدام أحدث إصدار
2. **راجع السجلات:** ابحث عن رسائل الخطأ التفصيلية
3. **اتصل بالدعم:** Discord: https://discord.gg/EbHgD4NEzP

### معلومات مطلوبة عند طلب المساعدة:
- إصدار ESX المستخدم
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة
- لقطة شاشة من F8

---

**نصائح للوقاية:**
- عمل نسخة احتياطية قبل التحديث
- اختبار السكربت في بيئة تطوير أولاً
- قراءة سجل التغييرات قبل التحديث
- التأكد من التوافق مع السكربتات الأخرى
