-- إصلاح oxmysql مبسط
-- Simple oxmysql fix

if IsDuplicityVersion() then
    -- Server-side simple oxmysql
    
    local MySQL = nil
    local isReady = false
    
    -- تحميل oxmysql بطريقة آمنة
    Citizen.CreateThread(function()
        Citizen.Wait(2000) -- انتظار تحميل oxmysql
        
        if GetResourceState('oxmysql') == 'started' then
            local success, result = pcall(function()
                return exports.oxmysql
            end)
            
            if success and result then
                MySQL = result
                isReady = true
                print('^2[OXMYSQL SIMPLE] ^7oxmysql loaded successfully')
                
                -- اختبار بسيط
                local testSuccess = pcall(function()
                    MySQL:execute('SELECT 1 as test', {}, function(result)
                        if result then
                            print('^2[OXMYSQL SIMPLE] ^7✓ Test query successful')
                        end
                    end)
                end)
                
                if not testSuccess then
                    print('^1[OXMYSQL SIMPLE] ^7Test query failed, trying alternative method')
                end
            else
                print('^1[OXMYSQL SIMPLE] ^7Failed to load oxmysql')
            end
        else
            print('^3[OXMYSQL SIMPLE] ^7oxmysql not available')
        end
    end)
    
    -- دالة تنفيذ آمنة
    local function safeExecute(query, params, callback)
        if not isReady or not MySQL then
            print('^1[OXMYSQL SIMPLE] ^7MySQL not ready')
            if callback then callback(0) end
            return
        end
        
        -- محاولة الطريقة الأولى
        local success = pcall(function()
            MySQL:execute(query, params, callback or function() end)
        end)
        
        if not success then
            -- محاولة الطريقة الثانية
            success = pcall(function()
                MySQL.execute(query, params, callback or function() end)
            end)
        end
        
        if not success then
            print('^1[OXMYSQL SIMPLE] ^7Execute failed for query: ' .. tostring(query))
            if callback then callback(0) end
        end
    end
    
    -- دالة جلب آمنة
    local function safeFetch(query, params, callback)
        if not isReady or not MySQL then
            print('^1[OXMYSQL SIMPLE] ^7MySQL not ready')
            if callback then callback({}) end
            return
        end
        
        -- محاولة الطريقة الأولى
        local success = pcall(function()
            MySQL:execute(query, params, callback or function() end)
        end)
        
        if not success then
            -- محاولة الطريقة الثانية
            success = pcall(function()
                MySQL.execute(query, params, callback or function() end)
            end)
        end
        
        if not success then
            print('^1[OXMYSQL SIMPLE] ^7Fetch failed for query: ' .. tostring(query))
            if callback then callback({}) end
        end
    end
    
    -- تصدير الدوال
    exports('safeExecute', safeExecute)
    exports('safeFetch', safeFetch)
    exports('isReady', function() return isReady end)
    
    -- أمر للاختبار
    RegisterCommand('test_oxmysql_simple', function(source)
        if source == 0 then
            print('^3[OXMYSQL SIMPLE] ^7Testing...')
            print('^3[OXMYSQL SIMPLE] ^7Ready: ' .. tostring(isReady))
            
            if isReady then
                safeFetch('SELECT 1 as test', {}, function(result)
                    if result then
                        print('^2[OXMYSQL SIMPLE] ^7✓ Simple test successful')
                    else
                        print('^1[OXMYSQL SIMPLE] ^7✗ Simple test failed')
                    end
                end)
            end
        end
    end, true)
    
    print('^2[OXMYSQL SIMPLE] ^7Simple oxmysql fix loaded')
end
