-- اختبار بسيط للتأكد من عمل callback
-- Simple test to verify callback is working

if not IsDuplicityVersion() then
    -- Client-side test
    
    local ESX = nil
    
    Citizen.CreateThread(function()
        while ESX == nil do
            ESX = exports.es_extended:getSharedObject()
            Citizen.Wait(100)
        end
        
        print('^2[SIMPLE TEST] ^7ESX loaded on client')
    end)
    
    -- أمر اختبار بسيط
    RegisterCommand('test_callback_simple', function()
        if ESX then
            print('^3[SIMPLE TEST] ^7Testing oscar:server:getBill callback...')
            
            ESX.TriggerServerCallback('oscar:server:getBill', function(result)
                if result ~= nil then
                    print('^2[SIMPLE TEST] ^7✓ SUCCESS! Callback working, bills: ' .. tostring(result))
                    ESX.ShowNotification('~g~✓ Callback يعمل بنجاح!')
                else
                    print('^1[SIMPLE TEST] ^7✗ FAILED! Callback not working')
                    ESX.ShowNotification('~r~✗ Callback لا يعمل!')
                end
            end, 'GetBils')
        else
            print('^1[SIMPLE TEST] ^7ESX not loaded!')
        end
    end, false)
    
    -- اختبار تلقائي بعد 10 ثوان
    Citizen.CreateThread(function()
        Citizen.Wait(10000) -- انتظار 10 ثوان
        
        if ESX then
            print('^3[AUTO TEST] ^7Running automatic callback test...')
            
            ESX.TriggerServerCallback('oscar:server:getBill', function(result)
                if result ~= nil then
                    print('^2[AUTO TEST] ^7✓ Automatic test PASSED! Bills: ' .. tostring(result))
                else
                    print('^1[AUTO TEST] ^7✗ Automatic test FAILED!')
                end
            end, 'GetBils')
        end
    end)
    
    print('^2[SIMPLE TEST] ^7Simple test client loaded')
end
