-- إصلاح مشكلة progress bar
-- Progress bar fix

if not IsDuplicityVersion() then
    -- Client-side fix for progress bar
    
    local ESX = nil
    
    Citizen.CreateThread(function()
        while ESX == nil do
            ESX = exports.es_extended:getSharedObject()
            Citizen.Wait(100)
        end
    end)
    
    -- إنشاء progress bar بديل إذا لم يكن الأصلي متاح
    local function safeProgressBar(duration, text)
        local success = pcall(function()
            exports.pogressBar:drawBar(duration, text)
        end)
        
        if not success then
            -- استخدام إشعار ESX كبديل
            if ESX then
                ESX.ShowNotification('~b~' .. text)
            end
            
            -- انتظار المدة المطلوبة
            Citizen.Wait(duration)
        end
    end
    
    -- تصدير الدالة الآمنة
    exports('safeProgressBar', safeProgressBar)
    
    -- أمر لاختبار progress bar
    RegisterCommand('test_progress', function()
        print('^3[PROGRESS FIX] ^7Testing progress bar...')
        
        safeProgressBar(2000, 'اختبار شريط التقدم')
        
        print('^2[PROGRESS FIX] ^7Progress bar test completed')
    end, false)
    
    print('^2[PROGRESS FIX] ^7Progress bar fix loaded')
end
