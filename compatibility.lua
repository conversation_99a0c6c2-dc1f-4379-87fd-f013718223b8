-- ملف التوافق لحل المشاكل الشائعة
-- Compatibility file for common issues

if IsDuplicityVersion() then
    -- Server-side compatibility fixes

    -- تحميل ESX أولاً
    local ESX = exports.es_extended:getSharedObject()

    -- إصلاح مشكلة callback المفقود - نسخة احتياطية
    if ESX and (not ESX.ServerCallbacks or not ESX.ServerCallbacks['oscar:server:getBill']) then
        ESX.RegisterServerCallback('oscar:server:getBill', function(source, cb, action)
            local xPlayer = ESX.GetPlayerFromId(source)

            if not xPlayer then
                cb(0)
                return
            end

            -- إرجاع 0 مخالفات افتراضياً
            cb(0)
            print('^3[GARAGE COMPATIBILITY] ^7Bill callback called for player: ' .. xPlayer.identifier)
        end)
    end
    
    -- إصلاح callback للمنازل إذا لم يكن موجود
    if ESX and ESX.ServerCallbacks and not ESX.ServerCallbacks['HyperScript_Garage:callback-server'] then
        ESX.RegisterServerCallback('HyperScript_Garage:callback-server', function(source, cb, action, data)
            local xPlayer = ESX.GetPlayerFromId(source)
            
            if not xPlayer then
                cb(nil)
                return
            end
            
            if action == 'getOwnedHouses' then
                -- إرجاع قائمة فارغة إذا لم يكن هناك نظام منازل
                cb({})
            elseif action == 'isCanStorage' then
                local plate = data.plate
                
                MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE plate = @plate', {
                    ['@plate'] = plate
                }, function(result)
                    if result[1] then
                        local vehicle = result[1]
                        local isOwner = vehicle.owner == xPlayer.identifier
                        
                        cb({
                            isOwner = isOwner,
                            vehType = vehicle.garage_type or 'car',
                            vehJob = vehicle.job or 'civ'
                        })
                    else
                        cb({
                            isOwner = false,
                            vehType = 'unknown',
                            vehJob = 'unknown'
                        })
                    end
                end)
            else
                cb(nil)
            end
        end)
    end
    
    -- إضافة معالج للأخطاء
    AddEventHandler('onResourceStart', function(resourceName)
        if GetCurrentResourceName() == resourceName then
            print('^2[GARAGE COMPATIBILITY] ^7Compatibility fixes loaded')
            
            -- التحقق من وجود الجداول المطلوبة (تم تعطيله لتجنب مشاكل MySQL)
            print('^2[GARAGE COMPATIBILITY] ^7Database check skipped')
        end
    end)
    
else
    -- Client-side compatibility fixes
    
    -- إصلاح مشكلة exports المفقودة
    local function safeExport(resource, exportName, fallback)
        local success, result = pcall(function()
            return exports[resource][exportName]
        end)
        
        if success then
            return result
        else
            print('^3[GARAGE WARNING] ^7Export ' .. resource .. ':' .. exportName .. ' not found, using fallback')
            return fallback
        end
    end
    
    -- إصلاح exports للإشعارات
    local function showNotification(type, message)
        local success = pcall(function()
            exports.OscarCounty_Notifications:showAttention(type, message)
        end)
        
        if not success then
            -- استخدام إشعارات ESX كبديل
            ESX.ShowNotification(message)
        end
    end
    
    -- إصلاح exports لشريط التقدم
    local function showProgressBar(duration, text)
        local success = pcall(function()
            exports.pogressBar:drawBar(duration, text)
        end)
        
        if not success then
            -- عرض رسالة بديلة
            ESX.ShowNotification(text)
            Wait(duration)
        end
    end
    
    -- إصلاح exports للرعاية
    local function isHaveSponsor()
        local success, result = pcall(function()
            return exports.OscarCounty_Web:method('isHaveSponsor')
        end)
        
        if success then
            return result
        else
            return false -- افتراضياً لا يوجد رعاية
        end
    end
    
    -- تصدير الدوال المصححة للاستخدام العام
    exports('showNotification', showNotification)
    exports('showProgressBar', showProgressBar)
    exports('isHaveSponsor', isHaveSponsor)
    
    -- إضافة أوامر للتصحيح
    RegisterCommand('garage_debug', function()
        print('^2[GARAGE DEBUG] ^7Running compatibility checks...')
        
        -- فحص ESX
        local ESX = exports.es_extended:getSharedObject()
        if ESX then
            print('^2[DEBUG] ^7ESX: OK')
            if ESX.PlayerData then
                print('^2[DEBUG] ^7Player Data: OK')
                print('^3[DEBUG] ^7Job: ' .. (ESX.PlayerData.job and ESX.PlayerData.job.name or 'unknown'))
            end
        else
            print('^1[DEBUG] ^7ESX: FAILED')
        end
        
        -- فحص الإشعارات
        local notifSuccess = pcall(function()
            exports.OscarCounty_Notifications:showAttention('info', 'Test notification')
        end)
        print('^' .. (notifSuccess and '2' or '1') .. '[DEBUG] ^7Notifications: ' .. (notifSuccess and 'OK' or 'FAILED'))
        
        -- فحص شريط التقدم
        local progressSuccess = pcall(function()
            exports.pogressBar:drawBar(1000, 'Test progress')
        end)
        print('^' .. (progressSuccess and '2' or '1') .. '[DEBUG] ^7Progress Bar: ' .. (progressSuccess and 'OK' or 'FAILED'))
        
        print('^2[GARAGE DEBUG] ^7Compatibility check completed')
    end, false)
    
    print('^2[GARAGE COMPATIBILITY] ^7Client compatibility fixes loaded')
end
